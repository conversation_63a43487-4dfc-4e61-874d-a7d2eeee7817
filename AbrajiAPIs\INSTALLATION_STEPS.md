# 🚀 خطوات تثبيت وتشغيل مشروع AbrajiAPIs مع إعدادات SAS Radius

## 📋 المتطلبات الأساسية
- XAMPP أو Laragon أو WAMP
- PHP 8.2 أو أحدث
- Composer
- Git (اختياري)

---

## 🔧 الخطوة 1: إصلاح تكوين PHP

### أ) إصلاح ملف php.ini في XAMPP:

1. **افتح ملف php.ini:**
   ```
   C:\xampp\php\php.ini
   ```

2. **ابحث عن السطر التالي وغيره:**
   ```ini
   ;extension_dir = "ext"
   ```
   **إلى:**
   ```ini
   extension_dir = "C:\xampp\php\ext"
   ```

3. **تأكد من تفعيل هذه الإضافات (احذف `;` من بدايتها):**
   ```ini
   extension=bz2
   extension=curl
   extension=fileinfo
   extension=gettext
   extension=mbstring
   extension=exif
   extension=mysqli
   extension=pdo_mysql
   extension=pdo_sqlite
   extension=openssl
   extension=ftp
   extension=gd
   extension=intl
   extension=zip
   ```

4. **احفظ الملف وأعد تشغيل Apache من XAMPP Control Panel**

---

## 🔧 الخطوة 2: تثبيت المشروع

### أ) انتقل إلى مجلد المشروع:
```cmd
cd /d E:\JOSN\Abraji-Website-copy-master\AbrajiAPIs
```

### ب) تثبيت Dependencies:
```cmd
C:\xampp\php\php.exe -d memory_limit=512M composer.phar install --no-dev --optimize-autoloader
```

### ج) إنشاء ملف .env:
```cmd
copy .env.example .env
```

### د) توليد مفتاح التطبيق:
```cmd
C:\xampp\php\php.exe artisan key:generate
```

---

## 🔧 الخطوة 3: إعداد قاعدة البيانات

### أ) إنشاء ملف قاعدة البيانات SQLite:
```cmd
echo. > database\database.sqlite
```

### ب) تشغيل Migrations:
```cmd
C:\xampp\php\php.exe artisan migrate
```

### ج) تشغيل Seeders (اختياري):
```cmd
C:\xampp\php\php.exe artisan db:seed
```

---

## 🔧 الخطوة 4: مسح Cache وإعداد التخزين

```cmd
C:\xampp\php\php.exe artisan config:clear
C:\xampp\php\php.exe artisan route:clear
C:\xampp\php\php.exe artisan cache:clear
C:\xampp\php\php.exe artisan storage:link
```

---

## 🔧 الخطوة 5: تشغيل الخادم

```cmd
C:\xampp\php\php.exe artisan serve
```

**أو إذا كنت تريد تحديد المنفذ:**
```cmd
C:\xampp\php\php.exe artisan serve --port=8080
```

---

## 🧪 الخطوة 6: اختبار النظام

### أ) زيارة التطبيق:
```
http://localhost:8000
```

### ب) اختبار API الأساسي:
```
http://localhost:8000/api/settings
```

### ج) اختبار اتصال SAS Radius:
```cmd
C:\xampp\php\php.exe artisan sas:test-connection 1
```

---

## 🔧 الخطوة 7: إعداد SAS Radius

### أ) عبر API:
```http
POST http://localhost:8000/api/settings
Authorization: Bearer {your_token}
Content-Type: application/json

{
    "sas_radius_url": "http://*************:8080",
    "connection_timeout": 30
}
```

### ب) عبر الواجهة:
1. سجل دخول للحصول على token
2. اذهب إلى `/settings`
3. أدخل URL أو IP الخاص بـ SAS Radius
4. اختبر الاتصال
5. احفظ الإعدادات

---

## 🔍 استكشاف الأخطاء

### مشكلة: PHP Extensions لا تعمل
**الحل:**
```cmd
# تحقق من الإضافات المحملة
C:\xampp\php\php.exe -m

# تحقق من ملف php.ini المستخدم
C:\xampp\php\php.exe --ini
```

### مشكلة: Migration فشل
**الحل:**
```cmd
# تحقق من قاعدة البيانات
C:\xampp\php\php.exe artisan migrate:status

# إعادة تشغيل Migration
C:\xampp\php\php.exe artisan migrate:fresh
```

### مشكلة: Composer بطيء
**الحل:**
```cmd
# استخدم memory limit أعلى
C:\xampp\php\php.exe -d memory_limit=1G composer.phar install
```

---

## 📊 الأوامر المفيدة

### إدارة الوحدات:
```cmd
# عرض جميع الوحدات
C:\xampp\php\php.exe artisan module:list

# تفعيل وحدة
C:\xampp\php\php.exe artisan module:enable Settings

# إلغاء تفعيل وحدة
C:\xampp\php\php.exe artisan module:disable Settings
```

### إدارة قاعدة البيانات:
```cmd
# إنشاء migration جديد
C:\xampp\php\php.exe artisan make:migration create_new_table

# تشغيل migrations محددة
C:\xampp\php\php.exe artisan migrate --path=/database/migrations/specific_migration.php

# التراجع عن migration
C:\xampp\php\php.exe artisan migrate:rollback
```

### اختبار SAS Radius:
```cmd
# اختبار اتصال لمدير محدد
C:\xampp\php\php.exe artisan sas:test-connection 1

# عرض جميع الإعدادات
C:\xampp\php\php.exe artisan tinker
>>> Modules\Settings\Models\Setting::all();
```

---

## 🎯 النتيجة النهائية

بعد إكمال هذه الخطوات، ستحصل على:

✅ **نظام مرن لإعدادات SAS Radius**
✅ **واجهة API كاملة لإدارة الإعدادات**
✅ **اختبار اتصال فوري**
✅ **إعدادات منفصلة لكل مدير**
✅ **حماية وأمان متقدم**
✅ **واجهة مستخدم بسيطة**

---

## 📞 الدعم

إذا واجهت أي مشاكل:

1. **تحقق من logs:**
   ```
   storage/logs/laravel.log
   ```

2. **تشغيل في وضع debug:**
   ```
   APP_DEBUG=true في ملف .env
   ```

3. **اختبار PHP:**
   ```cmd
   C:\xampp\php\php.exe -v
   C:\xampp\php\php.exe -m
   ```

🎉 **مبروك! تم تثبيت النظام بنجاح!**
