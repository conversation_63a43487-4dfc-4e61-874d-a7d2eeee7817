# Settings Module - SAS Radius Configuration

## Overview
This module allows users to configure their own SAS Radius server connection settings instead of using a fixed configuration. Each admin can set their own SAS Radius URL or IP address.

## Features

### 🔧 **Flexible Configuration**
- **URL-based**: Enter full SAS Radius URL (e.g., `http://*************:8080`)
- **IP-based**: Enter IP address and port separately
- **Connection timeout**: Configurable timeout settings
- **Per-admin settings**: Each admin has their own configuration

### 🧪 **Connection Testing**
- Test connection before saving
- Real-time validation
- Connection status feedback

### 🔒 **Security**
- JWT token-based authentication
- Admin-specific settings isolation
- Secure token validation

## API Endpoints

### Get Settings
```http
GET /api/settings
Authorization: Bearer {token}
```

**Response:**
```json
{
    "status": 200,
    "data": {
        "sas_radius_url": "http://*************",
        "sas_radius_ip": "*************",
        "sas_radius_port": 80,
        "connection_timeout": 30,
        "full_url": "http://*************",
        "is_active": true
    }
}
```

### Save Settings
```http
POST /api/settings
Authorization: Bearer {token}
Content-Type: application/json

{
    "sas_radius_url": "http://*************",
    "sas_radius_ip": "*************",
    "sas_radius_port": 80,
    "connection_timeout": 30
}
```

### Test Connection
```http
POST /api/settings/test-connection
Authorization: Bearer {token}
Content-Type: application/json

{
    "test_url": "http://*************",
    "connection_timeout": 30
}
```

## Database Schema

### Settings Table
```sql
CREATE TABLE settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    admin_id BIGINT NOT NULL,
    sas_radius_url VARCHAR(255) NULL,
    sas_radius_ip VARCHAR(255) NULL,
    sas_radius_port INT DEFAULT 80,
    connection_timeout INT DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(255) NULL,
    updated_by VARCHAR(255) NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_admin_id (admin_id),
    INDEX idx_admin_active (admin_id, is_active)
);
```

## Usage Examples

### Frontend Integration
```javascript
// Load current settings
fetch('/api/settings', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    console.log('Current settings:', data.data);
});

// Save new settings
fetch('/api/settings', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        sas_radius_url: 'http://*************:8080',
        connection_timeout: 45
    })
});

// Test connection
fetch('/api/settings/test-connection', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        test_url: 'http://*************:8080'
    })
});
```

### Backend Integration
```php
// Get SAS Radius URL for current admin
$sasRadiusUrl = SasRadiusUrlService::getUrlFromRequest($request);

// Get URL for specific admin
$sasRadiusUrl = SasRadiusUrlService::getUrlForAdmin($adminId);

// Use in API calls
$url = rtrim($sasRadiusUrl, '/') . '/admin/api/index.php/api/login';
```

## Installation

1. **Run Migration:**
```bash
php artisan migrate
```

2. **Register Module:**
The module is automatically registered via `modules_statuses.json`

3. **Update Routes:**
Routes are automatically loaded via the RouteServiceProvider

## Configuration Options

### Environment Variables
```env
# Default SAS Radius URL (fallback)
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost
```

### Module Config
```php
// config/settings.php
return [
    'default_settings' => [
        'sas_radius_port' => 80,
        'connection_timeout' => 30,
        'max_retries' => 3,
    ],
    'validation' => [
        'url_timeout' => 10,
        'ip_timeout' => 5,
    ]
];
```

## Validation Rules

- **sas_radius_url**: Must be valid URL format
- **sas_radius_ip**: Must be valid IP address
- **sas_radius_port**: Integer between 1-65535
- **connection_timeout**: Integer between 5-300 seconds
- **Either URL or IP required**: Cannot save empty configuration

## Error Handling

The module includes comprehensive error handling:
- Invalid token validation
- Connection timeout errors
- Invalid URL/IP format errors
- Database connection errors
- SAS Radius server unreachable errors

## Security Considerations

- All settings are admin-specific (isolated by admin_id)
- JWT token validation on all endpoints
- Input validation and sanitization
- SQL injection protection via Eloquent ORM
- HTTPS recommended for production use
