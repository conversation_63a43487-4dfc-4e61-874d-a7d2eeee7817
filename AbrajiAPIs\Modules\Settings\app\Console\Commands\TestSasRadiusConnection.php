<?php

namespace Modules\Settings\Console\Commands;

use Illuminate\Console\Command;
use Modules\Settings\Models\Setting;
use GuzzleHttp\Client;

class TestSasRadiusConnection extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sas:test-connection {admin_id? : Admin ID to test connection for}';

    /**
     * The console command description.
     */
    protected $description = 'Test SAS Radius connection for admin';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $adminId = $this->argument('admin_id');

        if (!$adminId) {
            $adminId = $this->ask('Enter Admin ID to test connection for');
        }

        $this->info("Testing SAS Radius connection for Admin ID: {$adminId}");

        $settings = Setting::forAdmin($adminId)->active()->first();

        if (!$settings) {
            $this->warn('No settings found for this admin. Using default configuration.');
            $url = config('app.api_domain');
        } else {
            $url = $settings->full_url;
            $this->info("Using configured URL: {$url}");
        }

        $this->info('Testing connection...');

        try {
            $client = new Client([
                'timeout' => $settings->connection_timeout ?? 30,
                'verify' => false
            ]);

            $testUrl = rtrim($url, '/') . '/admin/api/index.php/api/login';
            
            $response = $client->post($testUrl, [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => ['payload' => 'test_connection'],
                'http_errors' => false
            ]);

            $statusCode = $response->getStatusCode();
            
            if ($statusCode < 500) {
                $this->info("✅ Connection successful! Response code: {$statusCode}");
                $this->info("✅ SAS Radius server is reachable at: {$testUrl}");
                return Command::SUCCESS;
            } else {
                $this->error("❌ Connection failed! Response code: {$statusCode}");
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error("❌ Connection failed: " . $e->getMessage());
            $this->error("❌ URL tested: {$testUrl}");
            
            $this->newLine();
            $this->warn('Troubleshooting tips:');
            $this->line('1. Check if SAS Radius server is running');
            $this->line('2. Verify the URL/IP address is correct');
            $this->line('3. Check firewall settings');
            $this->line('4. Ensure the port is accessible');
            
            return Command::FAILURE;
        }
    }
}
