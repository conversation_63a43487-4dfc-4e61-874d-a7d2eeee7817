<?php

namespace Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Settings\Interfaces\SettingsServiceInterface;
use Modules\Settings\Http\Requests\SaveSettingsRequest;

class SettingsController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsServiceInterface $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Get current settings for the authenticated admin
     */
    public function index(Request $request)
    {
        return $this->settingsService->getSettings($request);
    }

    /**
     * Save or update settings
     */
    public function store(SaveSettingsRequest $request)
    {
        return $this->settingsService->saveSettings($request);
    }

    /**
     * Test connection to SAS Radius
     */
    public function testConnection(Request $request)
    {
        return $this->settingsService->testConnection($request);
    }
}
