<?php

namespace Modules\Settings\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SaveSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'sas_radius_url' => 'nullable|url|max:255',
            'sas_radius_ip' => 'nullable|ip|max:45',
            'sas_radius_port' => 'nullable|integer|min:1|max:65535',
            'connection_timeout' => 'nullable|integer|min:5|max:300',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'sas_radius_url.url' => 'SAS Radius URL must be a valid URL format (e.g., http://example.com)',
            'sas_radius_ip.ip' => 'SAS Radius IP must be a valid IP address',
            'sas_radius_port.integer' => 'Port must be a number',
            'sas_radius_port.min' => 'Port must be at least 1',
            'sas_radius_port.max' => 'Port must not exceed 65535',
            'connection_timeout.integer' => 'Connection timeout must be a number',
            'connection_timeout.min' => 'Connection timeout must be at least 5 seconds',
            'connection_timeout.max' => 'Connection timeout must not exceed 300 seconds (5 minutes)',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Ensure either URL or IP is provided
            if (!$this->sas_radius_url && !$this->sas_radius_ip) {
                $validator->errors()->add(
                    'sas_radius_url', 
                    'Either SAS Radius URL or IP address must be provided'
                );
            }

            // If IP is provided without port, set default
            if ($this->sas_radius_ip && !$this->sas_radius_port) {
                $this->merge(['sas_radius_port' => 80]);
            }

            // If timeout is not provided, set default
            if (!$this->connection_timeout) {
                $this->merge(['connection_timeout' => 30]);
            }
        });
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'sas_radius_url' => 'SAS Radius URL',
            'sas_radius_ip' => 'SAS Radius IP',
            'sas_radius_port' => 'SAS Radius Port',
            'connection_timeout' => 'Connection Timeout',
        ];
    }
}
