<?php

namespace Modules\Settings\Interfaces;

use Illuminate\Http\Request;

interface SettingsServiceInterface
{
    /**
     * Get settings for admin
     */
    public function getSettings(Request $request);

    /**
     * Create or update settings
     */
    public function saveSettings(Request $request);

    /**
     * Test connection to SAS Radius
     */
    public function testConnection(Request $request);

    /**
     * Get SAS Radius URL for admin
     */
    public function getSasRadiusUrl($adminId);
}
