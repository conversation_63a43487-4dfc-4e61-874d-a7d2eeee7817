<?php

namespace Modules\Settings\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Settings\Database\Factories\SettingFactory;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'admin_id',
        'sas_radius_url',
        'sas_radius_ip',
        'sas_radius_port',
        'connection_timeout',
        'is_active',
        'created_by',
        'updated_by'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'sas_radius_port' => 'integer',
        'connection_timeout' => 'integer',
    ];

    /**
     * Get the full SAS Radius URL
     */
    public function getFullUrlAttribute()
    {
        if ($this->sas_radius_url) {
            return $this->sas_radius_url;
        }
        
        if ($this->sas_radius_ip) {
            $port = $this->sas_radius_port ? ':' . $this->sas_radius_port : '';
            return 'http://' . $this->sas_radius_ip . $port;
        }
        
        return config('app.api_domain');
    }

    /**
     * Scope for active settings
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific admin
     */
    public function scopeForAdmin($query, $adminId)
    {
        return $query->where('admin_id', $adminId);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return SettingFactory::new();
    }
}
