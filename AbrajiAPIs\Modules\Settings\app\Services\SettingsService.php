<?php

namespace Modules\Settings\Services;

use Modules\Settings\Models\Setting;
use Modules\Settings\Interfaces\SettingsServiceInterface;
use Modules\Settings\Http\Resources\SettingResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SettingsService implements SettingsServiceInterface
{
    /**
     * Get admin ID from JWT token
     */
    private function getAdminIdFromToken($token)
    {
        try {
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                throw new \Exception('Invalid token format');
            }

            $payload = base64_decode($tokenParts[1]);
            $decoded = json_decode($payload);

            if (isset($decoded->sub)) {
                return $decoded->sub;
            } else {
                throw new \Exception('Admin ID not found in token');
            }
        } catch (\Exception $e) {
            throw new \Exception('Token validation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get settings for admin
     */
    public function getSettings(Request $request)
    {
        try {
            $authorization = $request->header('Authorization');
            $adminId = $this->getAdminIdFromToken($authorization);

            $settings = Setting::forAdmin($adminId)->active()->first();

            if (!$settings) {
                // Return default settings
                return response()->json([
                    'status' => 200,
                    'data' => [
                        'sas_radius_url' => config('app.api_domain'),
                        'sas_radius_ip' => null,
                        'sas_radius_port' => 80,
                        'connection_timeout' => 30,
                        'is_active' => true,
                        'is_default' => true
                    ]
                ], 200);
            }

            return response()->json([
                'status' => 200,
                'data' => [
                    'id' => $settings->id,
                    'sas_radius_url' => $settings->sas_radius_url,
                    'sas_radius_ip' => $settings->sas_radius_ip,
                    'sas_radius_port' => $settings->sas_radius_port,
                    'connection_timeout' => $settings->connection_timeout,
                    'full_url' => $settings->full_url,
                    'is_active' => $settings->is_active,
                    'is_default' => false
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update settings
     */
    public function saveSettings(Request $request)
    {
        try {
            $authorization = $request->header('Authorization');
            $adminId = $this->getAdminIdFromToken($authorization);

            // Validation rules
            $validator = Validator::make($request->all(), [
                'sas_radius_url' => 'nullable|url',
                'sas_radius_ip' => 'nullable|ip',
                'sas_radius_port' => 'nullable|integer|min:1|max:65535',
                'connection_timeout' => 'nullable|integer|min:5|max:300'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 422,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Ensure either URL or IP is provided
            if (!$request->sas_radius_url && !$request->sas_radius_ip) {
                return response()->json([
                    'status' => 422,
                    'error' => 'Either SAS Radius URL or IP address must be provided'
                ], 422);
            }

            // Find existing settings or create new
            $settings = Setting::forAdmin($adminId)->first();

            $data = [
                'admin_id' => $adminId,
                'sas_radius_url' => $request->sas_radius_url,
                'sas_radius_ip' => $request->sas_radius_ip,
                'sas_radius_port' => $request->sas_radius_port ?? 80,
                'connection_timeout' => $request->connection_timeout ?? 30,
                'is_active' => true,
                'updated_by' => $adminId
            ];

            if ($settings) {
                $settings->update($data);
            } else {
                $data['created_by'] = $adminId;
                $settings = Setting::create($data);
            }

            return response()->json([
                'status' => 200,
                'message' => 'Settings saved successfully',
                'data' => [
                    'id' => $settings->id,
                    'full_url' => $settings->full_url
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test connection to SAS Radius
     */
    public function testConnection(Request $request)
    {
        try {
            $authorization = $request->header('Authorization');
            $adminId = $this->getAdminIdFromToken($authorization);

            $url = $request->test_url;
            if (!$url) {
                $settings = Setting::forAdmin($adminId)->active()->first();
                $url = $settings ? $settings->full_url : config('app.api_domain');
            }

            // Test connection with a simple request
            $client = new \GuzzleHttp\Client([
                'timeout' => $request->connection_timeout ?? 30,
                'verify' => false // For testing purposes
            ]);

            $testUrl = rtrim($url, '/') . '/admin/api/index.php/api/login';
            
            $response = $client->post($testUrl, [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => ['payload' => 'test_connection'],
                'http_errors' => false
            ]);

            $statusCode = $response->getStatusCode();
            
            return response()->json([
                'status' => 200,
                'connection_status' => $statusCode < 500 ? 'success' : 'failed',
                'response_code' => $statusCode,
                'message' => $statusCode < 500 ? 'Connection successful' : 'Connection failed',
                'tested_url' => $testUrl
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 200,
                'connection_status' => 'failed',
                'message' => 'Connection failed: ' . $e->getMessage()
            ], 200);
        }
    }

    /**
     * Get SAS Radius URL for admin
     */
    public function getSasRadiusUrl($adminId)
    {
        $settings = Setting::forAdmin($adminId)->active()->first();
        return $settings ? $settings->full_url : config('app.api_domain');
    }
}
