{"name": "nwidart/settings", "description": "Settings module for managing SAS Radius configurations", "authors": [{"name": "AbrajiAPIs Team", "email": "<EMAIL>"}], "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.8"}, "extra": {"laravel": {"providers": [], "aliases": {}}}, "autoload": {"psr-4": {"Modules\\Settings\\": "app/", "Modules\\Settings\\Database\\Factories\\": "database/factories/", "Modules\\Settings\\Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Modules\\Settings\\Tests\\": "tests/"}}}