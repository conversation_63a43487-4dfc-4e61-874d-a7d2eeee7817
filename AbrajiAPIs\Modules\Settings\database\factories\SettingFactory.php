<?php

namespace Modules\Settings\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Settings\Models\Setting;

class SettingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Setting::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'admin_id' => $this->faker->numberBetween(1, 100),
            'sas_radius_url' => $this->faker->url(),
            'sas_radius_ip' => $this->faker->ipv4(),
            'sas_radius_port' => $this->faker->numberBetween(80, 8080),
            'connection_timeout' => $this->faker->numberBetween(10, 60),
            'is_active' => true,
            'created_by' => (string) $this->faker->numberBetween(1, 10),
            'updated_by' => (string) $this->faker->numberBetween(1, 10),
        ];
    }

    /**
     * Indicate that the setting is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the setting uses URL instead of IP.
     */
    public function withUrl(): static
    {
        return $this->state(fn (array $attributes) => [
            'sas_radius_ip' => null,
            'sas_radius_port' => null,
        ]);
    }

    /**
     * Indicate that the setting uses IP instead of URL.
     */
    public function withIp(): static
    {
        return $this->state(fn (array $attributes) => [
            'sas_radius_url' => null,
        ]);
    }
}
