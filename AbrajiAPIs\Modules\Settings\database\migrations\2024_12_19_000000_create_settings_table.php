<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('admin_id');
            $table->string('sas_radius_url')->nullable()->comment('Full URL like http://example.com');
            $table->string('sas_radius_ip')->nullable()->comment('IP address like ***********');
            $table->integer('sas_radius_port')->nullable()->default(80)->comment('Port number');
            $table->integer('connection_timeout')->default(30)->comment('Connection timeout in seconds');
            $table->boolean('is_active')->default(true);
            $table->string('created_by')->nullable();
            $table->string('updated_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('admin_id');
            $table->index(['admin_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
