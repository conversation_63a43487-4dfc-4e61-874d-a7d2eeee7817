<?php

namespace Modules\Settings\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Settings\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default settings for demo admin
        Setting::firstOrCreate(
            ['admin_id' => 1],
            [
                'sas_radius_url' => config('app.api_domain', 'http://localhost'),
                'sas_radius_ip' => null,
                'sas_radius_port' => 80,
                'connection_timeout' => 30,
                'is_active' => true,
                'created_by' => '1',
                'updated_by' => '1'
            ]
        );

        // You can add more default settings here if needed
        $this->command->info('Settings seeded successfully!');
    }
}
