@extends('settings::layouts.master')

@section('content')
    <div class="container">
        <h1>SAS Radius Settings</h1>
        
        <div class="card">
            <div class="card-header">
                <h3>Configure SAS Radius Connection</h3>
            </div>
            <div class="card-body">
                <form id="settingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sas_radius_url">SAS Radius URL</label>
                                <input type="url" class="form-control" id="sas_radius_url" name="sas_radius_url" 
                                       placeholder="http://example.com">
                                <small class="form-text text-muted">Full URL including protocol (http/https)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <strong>OR</strong>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="sas_radius_ip">IP Address</label>
                                <input type="text" class="form-control" id="sas_radius_ip" name="sas_radius_ip" 
                                       placeholder="***********">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="sas_radius_port">Port</label>
                                <input type="number" class="form-control" id="sas_radius_port" name="sas_radius_port" 
                                       value="80" min="1" max="65535">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="connection_timeout">Timeout (seconds)</label>
                                <input type="number" class="form-control" id="connection_timeout" name="connection_timeout" 
                                       value="30" min="5" max="300">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-info btn-block" id="testConnection">
                                    Test Connection
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div id="connectionResult" class="alert" style="display: none;"></div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                            <button type="button" class="btn btn-secondary" id="loadSettings">Load Current Settings</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Load current settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentSettings();
        });

        // Load current settings
        function loadCurrentSettings() {
            fetch('/api/settings', {
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 200) {
                    const settings = data.data;
                    document.getElementById('sas_radius_url').value = settings.sas_radius_url || '';
                    document.getElementById('sas_radius_ip').value = settings.sas_radius_ip || '';
                    document.getElementById('sas_radius_port').value = settings.sas_radius_port || 80;
                    document.getElementById('connection_timeout').value = settings.connection_timeout || 30;
                }
            })
            .catch(error => console.error('Error loading settings:', error));
        }

        // Test connection
        document.getElementById('testConnection').addEventListener('click', function() {
            const url = document.getElementById('sas_radius_url').value;
            const ip = document.getElementById('sas_radius_ip').value;
            const port = document.getElementById('sas_radius_port').value;
            const timeout = document.getElementById('connection_timeout').value;

            let testUrl = url;
            if (!testUrl && ip) {
                testUrl = 'http://' + ip + (port ? ':' + port : '');
            }

            if (!testUrl) {
                showResult('Please enter either URL or IP address', 'danger');
                return;
            }

            fetch('/api/settings/test-connection', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test_url: testUrl,
                    connection_timeout: timeout
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.connection_status === 'success') {
                    showResult('Connection successful! Response code: ' + data.response_code, 'success');
                } else {
                    showResult('Connection failed: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                showResult('Connection test failed: ' + error.message, 'danger');
            });
        });

        // Save settings
        document.getElementById('settingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            fetch('/api/settings', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 200) {
                    showResult('Settings saved successfully!', 'success');
                } else {
                    showResult('Error saving settings: ' + (data.error || 'Unknown error'), 'danger');
                }
            })
            .catch(error => {
                showResult('Error saving settings: ' + error.message, 'danger');
            });
        });

        // Load settings button
        document.getElementById('loadSettings').addEventListener('click', loadCurrentSettings);

        // Show result message
        function showResult(message, type) {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.className = 'alert alert-' + type;
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
            
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
    </script>
@endsection
