# Abraji APIs

## About Abraji APIs

Abraji APIs is a Laravel-based modular application built for managing financial transactions, invoices, debts, and user management. The application is built using Laravel 11 with a modular architecture using nwidart/laravel-modules.

## Modules

The application consists of the following modules:

- **Authentication** - User authentication and authorization
- **Card** - Card management system
- **Dashboard** - Admin dashboard and analytics
- **Debts** - Debt tracking and management
- **Invoice** - Invoice generation and management
- **Manager** - System management tools
- **Settings** - Application settings and configuration
- **Transaction** - Transaction processing and history
- **Users** - User management system
- **Wallet** - Digital wallet functionality

## Installation

### Prerequisites

- PHP 8.2 or higher
- Composer
- Node.js and npm
- MySQL or SQLite
- Docker (optional, for Laravel Sail)

### Local Installation

1. Clone the repository
2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Install Node.js dependencies:
   ```bash
   npm install
   ```

4. Copy environment file:
   ```bash
   cp .env.example .env
   ```

5. Generate application key:
   ```bash
   php artisan key:generate
   ```

6. Run database migrations:
   ```bash
   php artisan migrate
   ```

7. Start the development server:
   ```bash
   php artisan serve
   ```

### Docker Installation (Laravel Sail)

1. Install dependencies:
   ```bash
   composer install
   ```

2. Start Docker containers:
   ```bash
   ./vendor/bin/sail up -d
   ```

3. Run migrations:
   ```bash
   ./vendor/bin/sail artisan migrate
   ```

## API Documentation

The API endpoints are organized by modules. Each module provides its own set of endpoints under the `/api` prefix.

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License.
