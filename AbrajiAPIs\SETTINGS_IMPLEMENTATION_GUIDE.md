# 🔧 SAS Radius Settings Implementation Guide

## 📋 Overview
This guide explains how to implement the new flexible SAS Radius configuration system that allows each admin to set their own SAS Radius server URL or IP address.

## 🚀 What's New?

### ✅ **Before (Fixed Configuration)**
```php
// All users used the same SAS Radius URL
$url = config('app.api_domain') . '/admin/api/index.php/api/login';
```

### ✅ **After (Dynamic Configuration)**
```php
// Each admin can have their own SAS Radius URL
$sasRadiusUrl = SasRadiusUrlService::getUrlFromRequest($request);
$url = rtrim($sasRadiusUrl, '/') . '/admin/api/index.php/api/login';
```

## 📁 Files Created/Modified

### 🆕 **New Files Created:**
1. `Modules/Settings/` - Complete Settings module
2. `app/Services/SasRadiusUrlService.php` - Helper service
3. `.env.example` - Updated with new variables

### 🔄 **Modified Files:**
1. `Modules/Authentication/app/Http/Controllers/LoginController.php`
2. `Modules/Card/app/Services/CardService.php`
3. `Modules/Dashboard/app/Services/DashboardCardService.php`
4. `Modules/Users/<USER>/Services/UsersService.php`
5. `modules_statuses.json`

## 🛠️ Installation Steps

### 1. **Run Database Migration**
```bash
php artisan migrate
```

### 2. **Update Environment File**
```bash
# Add to your .env file
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost
```

### 3. **Clear Cache (if needed)**
```bash
php artisan config:clear
php artisan route:clear
php artisan cache:clear
```

## 🔌 API Usage

### **Get Current Settings**
```http
GET /api/settings
Authorization: Bearer {admin_token}
```

### **Save New Settings**
```http
POST /api/settings
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "sas_radius_url": "http://***********00:8080",
    "connection_timeout": 30
}
```

### **Test Connection**
```http
POST /api/settings/test-connection
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "test_url": "http://***********00:8080"
}
```

## 💻 Frontend Integration

### **HTML Form Example**
```html
<form id="settingsForm">
    <input type="url" name="sas_radius_url" placeholder="http://example.com">
    <input type="text" name="sas_radius_ip" placeholder="***********">
    <input type="number" name="sas_radius_port" value="80">
    <button type="submit">Save Settings</button>
    <button type="button" onclick="testConnection()">Test Connection</button>
</form>
```

### **JavaScript Example**
```javascript
// Save settings
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    fetch('/api/settings', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 200) {
            alert('Settings saved successfully!');
        }
    });
});

// Test connection
function testConnection() {
    const url = document.querySelector('[name="sas_radius_url"]').value;
    
    fetch('/api/settings/test-connection', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ test_url: url })
    })
    .then(response => response.json())
    .then(data => {
        alert('Connection: ' + data.connection_status);
    });
}
```

## 🔧 Backend Integration

### **For New Modules**
When creating new modules that need to connect to SAS Radius:

```php
<?php

namespace Modules\YourModule\Services;

use App\Services\SasRadiusUrlService;
use Illuminate\Http\Request;

class YourService
{
    public function callSasRadiusApi(Request $request)
    {
        // Get the admin's SAS Radius URL
        $sasRadiusUrl = SasRadiusUrlService::getUrlFromRequest($request);
        
        // Build the full API endpoint
        $url = rtrim($sasRadiusUrl, '/') . '/admin/api/index.php/api/your-endpoint';
        
        // Make the API call
        $client = new \GuzzleHttp\Client();
        $response = $client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => $request->header('Authorization'),
            ],
            'json' => $your_data
        ]);
        
        return json_decode($response->getBody()->getContents(), true);
    }
}
```

### **For Existing Modules**
Replace hardcoded URLs with dynamic ones:

```php
// OLD WAY ❌
$url = config('app.api_domain') . '/admin/api/index.php/api/endpoint';

// NEW WAY ✅
$sasRadiusUrl = SasRadiusUrlService::getUrlFromRequest($request);
$url = rtrim($sasRadiusUrl, '/') . '/admin/api/index.php/api/endpoint';
```

## 🗄️ Database Schema

### **Settings Table**
```sql
CREATE TABLE settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    admin_id BIGINT NOT NULL,
    sas_radius_url VARCHAR(255) NULL COMMENT 'Full URL like http://example.com',
    sas_radius_ip VARCHAR(255) NULL COMMENT 'IP address like ***********',
    sas_radius_port INT DEFAULT 80 COMMENT 'Port number',
    connection_timeout INT DEFAULT 30 COMMENT 'Connection timeout in seconds',
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(255) NULL,
    updated_by VARCHAR(255) NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_admin_id (admin_id),
    INDEX idx_admin_active (admin_id, is_active)
);
```

## 🔒 Security Features

1. **Admin Isolation**: Each admin can only see/modify their own settings
2. **JWT Validation**: All endpoints require valid JWT tokens
3. **Input Validation**: URL and IP format validation
4. **SQL Injection Protection**: Using Eloquent ORM
5. **Connection Testing**: Safe connection testing without exposing credentials

## 🧪 Testing

### **Manual Testing Steps**
1. Login as admin to get JWT token
2. Access settings page: `/settings` (if using web interface)
3. Enter SAS Radius URL or IP address
4. Test connection
5. Save settings
6. Verify other modules use the new URL

### **API Testing with Postman**
```json
// Collection: SAS Radius Settings
{
    "info": {
        "name": "SAS Radius Settings API"
    },
    "item": [
        {
            "name": "Get Settings",
            "request": {
                "method": "GET",
                "url": "{{base_url}}/api/settings",
                "header": [
                    {
                        "key": "Authorization",
                        "value": "Bearer {{token}}"
                    }
                ]
            }
        },
        {
            "name": "Save Settings",
            "request": {
                "method": "POST",
                "url": "{{base_url}}/api/settings",
                "header": [
                    {
                        "key": "Authorization",
                        "value": "Bearer {{token}}"
                    },
                    {
                        "key": "Content-Type",
                        "value": "application/json"
                    }
                ],
                "body": {
                    "raw": "{\n    \"sas_radius_url\": \"http://***********00:8080\",\n    \"connection_timeout\": 30\n}"
                }
            }
        }
    ]
}
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Settings not saving**
   - Check JWT token validity
   - Verify database connection
   - Check validation errors in response

2. **Connection test failing**
   - Verify SAS Radius server is running
   - Check firewall settings
   - Ensure correct URL/IP format

3. **Module not loading**
   - Check `modules_statuses.json` has `"Settings": true`
   - Run `php artisan config:clear`
   - Verify file permissions

### **Debug Commands**
```bash
# Check module status
php artisan module:list

# Check routes
php artisan route:list | grep settings

# Check database
php artisan migrate:status
```

## 📞 Support

For issues or questions:
1. Check the logs in `storage/logs/laravel.log`
2. Verify all files are in place
3. Test API endpoints with Postman
4. Check database for settings records

This implementation provides a flexible, secure, and user-friendly way for admins to configure their own SAS Radius connections!
