# 🚀 دليل البدء السريع - AbrajiAPIs على VPS

## 📋 الخطوات السريعة (5 دقائق)

### 1️⃣ **إعداد VPS تلقائ<|im_start|>**
```bash
# على VPS الجديد (Ubuntu)
wget https://raw.githubusercontent.com/your-repo/AbrajiAPIs/main/vps_setup_ubuntu.sh
sudo bash vps_setup_ubuntu.sh
```

### 2️⃣ **نشر المشروع**
```bash
# من جهازك المحلي
chmod +x deploy_to_vps.sh
./deploy_to_vps.sh
```

### 3️⃣ **اختبار النظام**
```bash
# على VPS
curl -I https://yourdomain.com
```

---

## 🔧 الأوامر الأساسية

### **على VPS:**
```bash
# تشغيل المراقبة
bash /home/<USER>/vps_monitor.sh

# النشر اليدوي
bash /home/<USER>/deploy.sh

# النسخ الاحتياطي
bash /home/<USER>/backup.sh

# عرض Logs
sudo tail -f /home/<USER>/AbrajiAPIs/storage/logs/laravel.log

# إعادة تشغيل الخدمات
sudo systemctl restart nginx php8.2-fpm mysql
```

### **من جهازك المحلي:**
```bash
# نشر تحديث جديد
./deploy_to_vps.sh

# الاتصال بـ VPS
ssh root@your-vps-ip

# نسخ ملفات
scp file.txt root@your-vps-ip:/home/<USER>/
```

---

## 🌐 الروابط المهمة

بعد النشر، ستكون هذه الروابط متاحة:

- **الموقع الرئيسي:** `https://yourdomain.com`
- **API Settings:** `https://yourdomain.com/api/settings`
- **API Auth:** `https://yourdomain.com/api/auth/login`
- **API Test:** `https://yourdomain.com/api/settings/test-connection`

---

## 🔑 إعداد SAS Radius

### **عبر API:**
```bash
curl -X POST https://yourdomain.com/api/settings \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sas_radius_url": "http://*************:8080",
    "connection_timeout": 30
  }'
```

### **اختبار الاتصال:**
```bash
curl -X POST https://yourdomain.com/api/settings/test-connection \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "test_url": "http://*************:8080"
  }'
```

---

## 🚨 استكشاف الأخطاء السريع

### **الموقع لا يعمل:**
```bash
# فحص الخدمات
sudo systemctl status nginx php8.2-fpm mysql

# فحص Logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /home/<USER>/AbrajiAPIs/storage/logs/laravel.log

# إعادة تشغيل
sudo systemctl restart nginx php8.2-fpm
```

### **مشكلة في قاعدة البيانات:**
```bash
# اختبار الاتصال
mysql -u abrajiapis -p abrajiapis -e "SELECT 1"

# تشغيل Migrations
sudo -u abrajiapis php /home/<USER>/AbrajiAPIs/artisan migrate
```

### **مشكلة في الأذونات:**
```bash
# إصلاح الأذونات
sudo chown -R abrajiapis:www-data /home/<USER>/AbrajiAPIs
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/storage
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/bootstrap/cache
```

---

## 📊 المراقبة والصيانة

### **مراقبة يومية:**
```bash
# تشغيل فحص شامل
bash /home/<USER>/vps_monitor.sh

# فحص المساحة
df -h

# فحص الذاكرة
free -h

# فحص العمليات
htop
```

### **صيانة أسبوعية:**
```bash
# تنظيف الملفات القديمة
bash /home/<USER>/vps_monitor.sh cleanup

# تحديث النظام
sudo apt update && sudo apt upgrade

# فحص SSL
bash /home/<USER>/vps_monitor.sh check-website
```

---

## 🔐 الأمان

### **تحديث كلمات المرور:**
```bash
# تغيير كلمة مرور قاعدة البيانات
mysql -u root -p -e "ALTER USER 'abrajiapis'@'localhost' IDENTIFIED BY 'new_password';"

# تحديث .env
sudo -u abrajiapis nano /home/<USER>/AbrajiAPIs/.env
```

### **تحديث SSL:**
```bash
# تجديد الشهادة
sudo certbot renew

# فحص انتهاء الصلاحية
sudo certbot certificates
```

---

## 📁 مواقع الملفات المهمة

| الملف/المجلد | المسار |
|--------------|--------|
| **المشروع** | `/home/<USER>/AbrajiAPIs/` |
| **إعدادات البيئة** | `/home/<USER>/AbrajiAPIs/.env` |
| **Nginx Config** | `/etc/nginx/sites-available/abrajiapis` |
| **PHP Config** | `/etc/php/8.2/fpm/php.ini` |
| **MySQL Config** | `/etc/mysql/mysql.conf.d/mysqld.cnf` |
| **Logs** | `/home/<USER>/AbrajiAPIs/storage/logs/` |
| **Backups** | `/home/<USER>/backups/` |
| **Scripts** | `/home/<USER>/*.sh` |

---

## 🆘 الدعم الطارئ

### **إذا تعطل الموقع:**
```bash
# 1. فحص سريع
sudo systemctl status nginx php8.2-fpm mysql

# 2. إعادة تشغيل الخدمات
sudo systemctl restart nginx php8.2-fpm mysql

# 3. فحص المساحة
df -h

# 4. فحص Logs
sudo tail -20 /var/log/nginx/error.log
sudo tail -20 /home/<USER>/AbrajiAPIs/storage/logs/laravel.log

# 5. استعادة من النسخة الاحتياطية (إذا لزم الأمر)
cd /home/<USER>/backups
ls -la
# استعادة آخر نسخة احتياطية
```

### **أرقام الطوارئ:**
- **فحص الحالة:** `bash /home/<USER>/vps_monitor.sh`
- **تقرير شامل:** `bash /home/<USER>/vps_monitor.sh report`
- **إعادة النشر:** `bash /home/<USER>/deploy.sh`

---

## 🎯 نصائح للأداء

### **تحسين سرعة الموقع:**
```bash
# تفعيل OPcache
sudo nano /etc/php/8.2/fpm/conf.d/10-opcache.ini

# ضغط Gzip (مفعل افتراض<|im_start|>)
# تحسين MySQL
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# مسح Cache
sudo -u abrajiapis php /home/<USER>/AbrajiAPIs/artisan config:cache
sudo -u abrajiapis php /home/<USER>/AbrajiAPIs/artisan route:cache
sudo -u abrajiapis php /home/<USER>/AbrajiAPIs/artisan view:cache
```

### **مراقبة الأداء:**
```bash
# فحص استخدام الموارد
htop

# فحص اتصالات قاعدة البيانات
mysql -u root -p -e "SHOW PROCESSLIST;"

# فحص Nginx connections
sudo nginx -T
```

---

## 🎉 تهانينا!

موقعك الآن يعمل بكفاءة على VPS! 

**لا تنس:**
- ✅ إعداد مراقبة تلقائية
- ✅ جدولة النسخ الاحتياطية
- ✅ تحديث كلمات المرور
- ✅ مراقبة الأداء بانتظام

**للدعم:** راجع `VPS_SETUP_GUIDE.md` للتفاصيل الكاملة
