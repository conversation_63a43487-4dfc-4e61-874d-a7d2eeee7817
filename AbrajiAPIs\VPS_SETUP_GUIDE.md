# 🚀 دليل إعداد AbrajiAPIs على VPS

## 📋 المتطلبات الأساسية

### 🖥️ **مواصفات VPS المطلوبة:**
- **RAM:** 2GB كحد أدنى (4GB مفضل)
- **Storage:** 20GB كحد أدنى
- **CPU:** 1 Core كحد أدنى (2 Cores مفضل)
- **OS:** Ubuntu 20.04/22.04 LTS أو CentOS 8+

### 🔧 **البرامج المطلوبة:**
- PHP 8.2+
- Nginx أو Apache
- MySQL 8.0+ أو MariaDB 10.6+
- Composer
- Node.js 18+ (اختياري)
- SSL Certificate

---

## 🔧 الخطوة 1: إعداد الخادم الأساسي

### أ) تحديث النظام (Ubuntu):
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git unzip software-properties-common
```

### ب) تحديث النظام (CentOS):
```bash
sudo dnf update -y
sudo dnf install -y curl wget git unzip epel-release
```

---

## 🔧 الخطوة 2: تثبيت PHP 8.2

### أ) Ubuntu:
```bash
# إضافة repository
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update

# تثبيت PHP والإضافات المطلوبة
sudo apt install -y php8.2 php8.2-fpm php8.2-cli php8.2-common \
    php8.2-mysql php8.2-zip php8.2-gd php8.2-mbstring \
    php8.2-curl php8.2-xml php8.2-bcmath php8.2-json \
    php8.2-intl php8.2-soap php8.2-sqlite3 php8.2-fileinfo
```

### ب) CentOS:
```bash
# إضافة Remi repository
sudo dnf install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm
sudo dnf module enable php:remi-8.2 -y

# تثبيت PHP والإضافات
sudo dnf install -y php php-fpm php-cli php-common \
    php-mysql php-zip php-gd php-mbstring \
    php-curl php-xml php-bcmath php-json \
    php-intl php-soap php-sqlite3 php-fileinfo
```

### ج) تكوين PHP:
```bash
# تحرير ملف php.ini
sudo nano /etc/php/8.2/fpm/php.ini

# الإعدادات المطلوبة:
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 100M
date.timezone = UTC
```

---

## 🔧 الخطوة 3: تثبيت قاعدة البيانات

### أ) تثبيت MySQL (Ubuntu):
```bash
sudo apt install -y mysql-server
sudo mysql_secure_installation
```

### ب) تثبيت MariaDB (CentOS):
```bash
sudo dnf install -y mariadb-server mariadb
sudo systemctl start mariadb
sudo systemctl enable mariadb
sudo mysql_secure_installation
```

### ج) إنشاء قاعدة البيانات:
```bash
sudo mysql -u root -p

CREATE DATABASE abrajiapis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'abrajiapis'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON abrajiapis.* TO 'abrajiapis'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

---

## 🔧 الخطوة 4: تثبيت Nginx

### أ) تثبيت Nginx:
```bash
# Ubuntu
sudo apt install -y nginx

# CentOS
sudo dnf install -y nginx
```

### ب) تشغيل وتفعيل Nginx:
```bash
sudo systemctl start nginx
sudo systemctl enable nginx
sudo systemctl start php8.2-fpm
sudo systemctl enable php8.2-fpm
```

---

## 🔧 الخطوة 5: تثبيت Composer

```bash
# تحميل وتثبيت Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# التحقق من التثبيت
composer --version
```

---

## 🔧 الخطوة 6: إعداد المشروع

### أ) إنشاء مستخدم للمشروع:
```bash
sudo adduser abrajiapis
sudo usermod -aG www-data abrajiapis
```

### ب) رفع ملفات المشروع:
```bash
# الانتقال إلى مجلد المستخدم
sudo su - abrajiapis
cd /home/<USER>

# رفع المشروع (اختر إحدى الطرق)
# الطريقة 1: Git Clone
git clone https://github.com/your-repo/AbrajiAPIs.git
cd AbrajiAPIs

# الطريقة 2: رفع ملف ZIP
# ارفع الملف عبر SCP أو SFTP ثم:
unzip AbrajiAPIs.zip
cd AbrajiAPIs

# الطريقة 3: نسخ من الخادم المحلي
# scp -r /path/to/local/AbrajiAPIs user@vps_ip:/home/<USER>/
```

### ج) تثبيت Dependencies:
```bash
# تثبيت Composer packages
composer install --no-dev --optimize-autoloader

# إنشاء ملف .env
cp .env.example .env
```

### د) تكوين ملف .env:
```bash
nano .env

# تحديث الإعدادات:
APP_NAME=AbrajiAPIs
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=abrajiapis
DB_USERNAME=abrajiapis
DB_PASSWORD=strong_password_here

# إعدادات SAS Radius
API_DOMAIN=http://your-sas-radius-server.com
SAS_RADIUS_DEFAULT_URL=http://your-sas-radius-server.com
```

### هـ) إعداد Laravel:
```bash
# توليد مفتاح التطبيق
php artisan key:generate

# تشغيل Migrations
php artisan migrate --force

# تشغيل Seeders (اختياري)
php artisan db:seed --force

# مسح Cache
php artisan config:cache
php artisan route:cache
php artisan view:cache

# إنشاء Storage Links
php artisan storage:link
```

---

## 🔧 الخطوة 7: تكوين Nginx

### أ) إنشاء ملف تكوين الموقع:
```bash
sudo nano /etc/nginx/sites-available/abrajiapis
```

### ب) محتوى ملف التكوين:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    root /home/<USER>/AbrajiAPIs/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security: Hide sensitive files
    location ~ /\.(env|git) {
        deny all;
        return 404;
    }

    location /storage {
        alias /home/<USER>/AbrajiAPIs/storage/app/public;
    }
}
```

### ج) تفعيل الموقع:
```bash
sudo ln -s /etc/nginx/sites-available/abrajiapis /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔧 الخطوة 8: إعداد SSL Certificate

### أ) تثبيت Certbot:
```bash
# Ubuntu
sudo apt install -y certbot python3-certbot-nginx

# CentOS
sudo dnf install -y certbot python3-certbot-nginx
```

### ب) الحصول على SSL Certificate:
```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### ج) تجديد تلقائي للشهادة:
```bash
sudo crontab -e

# إضافة هذا السطر:
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🔧 الخطوة 9: إعداد الأذونات

```bash
# تعيين المالك الصحيح
sudo chown -R abrajiapis:www-data /home/<USER>/AbrajiAPIs

# تعيين الأذونات
sudo find /home/<USER>/AbrajiAPIs -type f -exec chmod 644 {} \;
sudo find /home/<USER>/AbrajiAPIs -type d -exec chmod 755 {} \;

# أذونات خاصة للمجلدات المطلوبة
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/storage
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/bootstrap/cache
```

---

## 🔧 الخطوة 10: إعداد Firewall

```bash
# Ubuntu (UFW)
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw enable

# CentOS (Firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

---

## 🔧 الخطوة 11: إعداد المراقبة والصيانة

### أ) إنشاء script للنسخ الاحتياطي:
```bash
sudo nano /home/<USER>/backup.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
PROJECT_DIR="/home/<USER>/AbrajiAPIs"

mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
mysqldump -u abrajiapis -p abrajiapis > $BACKUP_DIR/database_$DATE.sql

# نسخ احتياطي للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz $PROJECT_DIR

# حذف النسخ القديمة (أكثر من 7 أيام)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
chmod +x /home/<USER>/backup.sh

# إضافة إلى crontab للتشغيل اليومي
crontab -e
0 2 * * * /home/<USER>/backup.sh
```

### ب) إعداد Log Rotation:
```bash
sudo nano /etc/logrotate.d/abrajiapis
```

```
/home/<USER>/AbrajiAPIs/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 abrajiapis www-data
}
```

---

## 🧪 الخطوة 12: اختبار النظام

### أ) اختبار الموقع:
```bash
curl -I https://yourdomain.com
```

### ب) اختبار APIs:
```bash
# اختبار API الأساسي
curl -X GET https://yourdomain.com/api/settings \
  -H "Authorization: Bearer your_token_here"

# اختبار اتصال SAS Radius
curl -X POST https://yourdomain.com/api/settings/test-connection \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{"test_url": "http://your-sas-radius-server.com"}'
```

### ج) اختبار الأداء:
```bash
# تثبيت أدوات الاختبار
sudo apt install -y apache2-utils

# اختبار الحمولة
ab -n 100 -c 10 https://yourdomain.com/
```

---

## 🔧 الخطوة 13: تحسين الأداء

### أ) تفعيل OPcache:
```bash
sudo nano /etc/php/8.2/fpm/conf.d/10-opcache.ini
```

```ini
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### ب) تحسين MySQL:
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 64M
```

### ج) إعادة تشغيل الخدمات:
```bash
sudo systemctl restart php8.2-fpm
sudo systemctl restart nginx
sudo systemctl restart mysql
```

---

## 🚨 استكشاف الأخطاء الشائعة

### 1. خطأ 500 Internal Server Error:
```bash
# فحص logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /home/<USER>/AbrajiAPIs/storage/logs/laravel.log

# فحص الأذونات
ls -la /home/<USER>/AbrajiAPIs/storage/
```

### 2. مشكلة في قاعدة البيانات:
```bash
# اختبار الاتصال
mysql -u abrajiapis -p -e "SELECT 1"

# فحص حالة الجداول
php artisan migrate:status
```

### 3. مشكلة في PHP-FPM:
```bash
# فحص حالة الخدمة
sudo systemctl status php8.2-fpm

# فحص logs
sudo tail -f /var/log/php8.2-fpm.log
```

---

## 📊 مراقبة النظام

### أ) إعداد مراقبة الموارد:
```bash
# تثبيت htop
sudo apt install -y htop

# مراقبة استخدام الذاكرة والمعالج
htop
```

### ب) مراقبة المساحة:
```bash
# فحص المساحة المتاحة
df -h

# فحص أكبر الملفات
du -sh /home/<USER>/AbrajiAPIs/* | sort -hr
```

---

## 🎯 الخلاصة

بعد إكمال هذه الخطوات، ستحصل على:

✅ **خادم VPS محسن للأداء**
✅ **نظام AbrajiAPIs يعمل بكفاءة**
✅ **SSL Certificate للأمان**
✅ **نسخ احتياطية تلقائية**
✅ **مراقبة وصيانة دورية**
✅ **إعدادات SAS Radius مرنة**

🚀 **موقعك الآن جاهز للإنتاج!**
