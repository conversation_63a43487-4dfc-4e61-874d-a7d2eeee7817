<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Invoice;
use App\Models\Debt;
use App\Models\Maintenance;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * الحصول على بيانات لوحة التحكم الرئيسية
     * GET /api/dashboard
     */
    public function index(): JsonResponse
    {
        // إحصائيات المستخدمين
        $totalUsers = User::count();
        $activeUsers = User::where('status', 'active')->count();
        $onlineUsers = User::where('is_online', true)->count();
        $newUsersToday = User::whereDate('created_at', today())->count();

        // إحصائيات المالية
        $totalIncome = Transaction::where('type', 'in')->sum('amount');
        $totalExpenses = Transaction::where('type', 'out')->sum('amount');
        $netProfit = $totalIncome - $totalExpenses;
        $monthlyIncome = Transaction::where('type', 'in')
            ->whereMonth('created_at', now()->month)
            ->sum('amount');

        // إحصائيات الفواتير
        $totalInvoices = Invoice::count();
        $pendingInvoices = Invoice::where('status', 'pending')->count();
        $paidInvoices = Invoice::where('status', 'paid')->count();
        $overdueInvoices = Invoice::where('due_date', '<', now())
            ->where('status', '!=', 'paid')->count();

        // إحصائيات الديون
        $totalDebts = Debt::where('is_paid', false)->sum('amount');
        $paidDebts = Debt::where('is_paid', true)->sum('amount');
        $unpaidDebtsCount = Debt::where('is_paid', false)->count();

        // إحصائيات الصيانة
        $maintenanceCosts = Maintenance::whereMonth('created_at', now()->month)->sum('cost');
        $maintenanceCount = Maintenance::whereMonth('created_at', now()->month)->count();

        return response()->json([
            'success' => true,
            'data' => [
                'users' => [
                    'total' => $totalUsers,
                    'active' => $activeUsers,
                    'online' => $onlineUsers,
                    'new_today' => $newUsersToday,
                ],
                'financial' => [
                    'total_income' => number_format($totalIncome, 2),
                    'total_expenses' => number_format($totalExpenses, 2),
                    'net_profit' => number_format($netProfit, 2),
                    'monthly_income' => number_format($monthlyIncome, 2),
                ],
                'invoices' => [
                    'total' => $totalInvoices,
                    'pending' => $pendingInvoices,
                    'paid' => $paidInvoices,
                    'overdue' => $overdueInvoices,
                ],
                'debts' => [
                    'total_amount' => number_format($totalDebts, 2),
                    'paid_amount' => number_format($paidDebts, 2),
                    'unpaid_count' => $unpaidDebtsCount,
                ],
                'maintenance' => [
                    'monthly_cost' => number_format($maintenanceCosts, 2),
                    'monthly_count' => $maintenanceCount,
                ],
                'last_updated' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * الحصول على بطاقات لوحة التحكم
     * GET /api/dashboard/cards
     */
    public function cards(): JsonResponse
    {
        $cards = [
            [
                'id' => 1,
                'title' => 'إجمالي المستخدمين',
                'value' => User::count(),
                'icon' => 'users',
                'color' => 'blue',
                'change' => '+12%',
                'change_type' => 'increase'
            ],
            [
                'id' => 2,
                'title' => 'المستخدمين المتصلين',
                'value' => User::where('is_online', true)->count(),
                'icon' => 'user-check',
                'color' => 'green',
                'change' => '+5%',
                'change_type' => 'increase'
            ],
            [
                'id' => 3,
                'title' => 'إجمالي الدخل',
                'value' => number_format(Transaction::where('type', 'in')->sum('amount'), 2),
                'icon' => 'dollar-sign',
                'color' => 'green',
                'change' => '+8%',
                'change_type' => 'increase'
            ],
            [
                'id' => 4,
                'title' => 'الفواتير المعلقة',
                'value' => Invoice::where('status', 'pending')->count(),
                'icon' => 'file-text',
                'color' => 'orange',
                'change' => '-3%',
                'change_type' => 'decrease'
            ],
            [
                'id' => 5,
                'title' => 'الديون غير المدفوعة',
                'value' => number_format(Debt::where('is_paid', false)->sum('amount'), 2),
                'icon' => 'alert-circle',
                'color' => 'red',
                'change' => '+2%',
                'change_type' => 'increase'
            ],
            [
                'id' => 6,
                'title' => 'تكاليف الصيانة الشهرية',
                'value' => number_format(Maintenance::whereMonth('created_at', now()->month)->sum('cost'), 2),
                'icon' => 'tool',
                'color' => 'purple',
                'change' => '+15%',
                'change_type' => 'increase'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $cards
        ]);
    }

    /**
     * الحصول على المعاملات الأخيرة للوحة التحكم
     * GET /api/dashboard/transactions
     */
    public function transactions(): JsonResponse
    {
        $recentTransactions = Transaction::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($transaction) {
                return [
                    'id' => $transaction->id,
                    'user_id' => $transaction->user_id,
                    'user_name' => $transaction->user->name ?? 'غير محدد',
                    'amount' => number_format($transaction->amount, 2),
                    'type' => $transaction->type,
                    'type_text' => $transaction->type === 'in' ? 'دخل' : 'مصروف',
                    'description' => $transaction->description,
                    'category' => $transaction->category,
                    'status' => $transaction->status,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                    'created_at_human' => $transaction->created_at->diffForHumans(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $recentTransactions
        ]);
    }

    /**
     * الحصول على الرسوم البيانية
     * GET /api/dashboard/charts
     */
    public function charts(): JsonResponse
    {
        // رسم بياني للدخل الشهري
        $monthlyIncome = Transaction::where('type', 'in')
            ->selectRaw('MONTH(created_at) as month, SUM(amount) as total')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('total', 'month');

        // رسم بياني للمصروفات الشهرية
        $monthlyExpenses = Transaction::where('type', 'out')
            ->selectRaw('MONTH(created_at) as month, SUM(amount) as total')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('total', 'month');

        // رسم بياني للمستخدمين الجدد
        $newUsersMonthly = User::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('count', 'month');

        // إحصائيات الفواتير حسب الحالة
        $invoicesByStatus = Invoice::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        return response()->json([
            'success' => true,
            'data' => [
                'monthly_income' => $monthlyIncome,
                'monthly_expenses' => $monthlyExpenses,
                'new_users_monthly' => $newUsersMonthly,
                'invoices_by_status' => $invoicesByStatus,
            ]
        ]);
    }

    /**
     * الحصول على الأنشطة الأخيرة
     * GET /api/dashboard/activities
     */
    public function activities(): JsonResponse
    {
        $activities = collect();

        // أحدث المستخدمين
        $newUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function($user) {
                return [
                    'type' => 'user_registered',
                    'title' => 'مستخدم جديد',
                    'description' => "انضم {$user->name} إلى النظام",
                    'icon' => 'user-plus',
                    'color' => 'green',
                    'created_at' => $user->created_at,
                ];
            });

        // أحدث المعاملات
        $newTransactions = Transaction::orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function($transaction) {
                return [
                    'type' => 'transaction',
                    'title' => $transaction->type === 'in' ? 'دخل جديد' : 'مصروف جديد',
                    'description' => $transaction->description,
                    'icon' => $transaction->type === 'in' ? 'arrow-up' : 'arrow-down',
                    'color' => $transaction->type === 'in' ? 'green' : 'red',
                    'created_at' => $transaction->created_at,
                ];
            });

        // دمج الأنشطة وترتيبها
        $activities = $activities->merge($newUsers)
            ->merge($newTransactions)
            ->sortByDesc('created_at')
            ->take(10)
            ->values();

        return response()->json([
            'success' => true,
            'data' => $activities
        ]);
    }
}
