<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Invoice;
use App\Models\InvoiceItem;

class InvoicesController extends Controller
{
    /**
     * الحصول على جميع الفواتير
     * GET /api/invoices
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        
        $query = Invoice::with(['user', 'items']);
        
        if ($status) {
            $query->where('status', $status);
        }
        
        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }
        
        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }
        
        $invoices = $query->orderBy('created_at', 'desc')->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $invoices->items(),
            'pagination' => [
                'current_page' => $invoices->currentPage(),
                'last_page' => $invoices->lastPage(),
                'per_page' => $invoices->perPage(),
                'total' => $invoices->total(),
            ]
        ]);
    }

    /**
     * إنشاء فاتورة جديدة
     * POST /api/invoices
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'username' => 'required|string|max:255',
            'due_date' => 'required|date|after:today',
            'type' => 'required|string|max:100',
            'amount' => 'required|numeric|min:0',
            'description' => 'required|string|max:500',
            'payment_method' => 'required|string|max:100',
            'discount' => 'sometimes|numeric|min:0|max:100',
            'discount_value' => 'sometimes|numeric|min:0',
            'invoice_items' => 'sometimes|array',
            'invoice_items.*.name' => 'required_with:invoice_items|string|max:255',
            'invoice_items.*.quantity' => 'required_with:invoice_items|integer|min:1',
            'invoice_items.*.price' => 'required_with:invoice_items|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            // حساب الإجمالي
            $amount = $request->amount;
            $discount = $request->discount ?? 0;
            $discountValue = $request->discount_value ?? ($amount * ($discount / 100));
            $total = $amount - $discountValue;

            $invoice = Invoice::create([
                'user_id' => $request->user_id,
                'username' => $request->username,
                'due_date' => $request->due_date,
                'type' => $request->type,
                'amount' => $amount,
                'description' => $request->description,
                'created_by' => auth()->id(),
                'discount' => $discount,
                'discount_value' => $discountValue,
                'total' => $total,
                'payment_method' => $request->payment_method,
                'status' => 'pending',
            ]);

            // إضافة عناصر الفاتورة
            if ($request->has('invoice_items')) {
                foreach ($request->invoice_items as $item) {
                    InvoiceItem::create([
                        'invoice_id' => $invoice->id,
                        'name' => $item['name'],
                        'quantity' => $item['quantity'],
                        'price' => $item['price'],
                        'total' => $item['quantity'] * $item['price'],
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الفاتورة بنجاح',
                'data' => [
                    'invoice' => [
                        'id' => $invoice->id,
                        'user_id' => $invoice->user_id,
                        'username' => $invoice->username,
                        'due_date' => $invoice->due_date,
                        'amount' => number_format($invoice->amount, 2),
                        'total' => number_format($invoice->total, 2),
                        'status' => $invoice->status,
                        'created_at' => $invoice->created_at,
                    ]
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الفاتورة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على فاتورة بالمعرف
     * GET /api/invoices/{id}
     */
    public function show($id): JsonResponse
    {
        $invoice = Invoice::with(['user', 'items'])->find($id);
        
        if (!$invoice) {
            return response()->json([
                'success' => false,
                'message' => 'الفاتورة غير موجودة'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'invoice' => [
                    'id' => $invoice->id,
                    'user_id' => $invoice->user_id,
                    'user_name' => $invoice->user->name ?? 'غير محدد',
                    'username' => $invoice->username,
                    'due_date' => $invoice->due_date,
                    'type' => $invoice->type,
                    'amount' => number_format($invoice->amount, 2),
                    'description' => $invoice->description,
                    'discount' => $invoice->discount,
                    'discount_value' => number_format($invoice->discount_value, 2),
                    'total' => number_format($invoice->total, 2),
                    'payment_method' => $invoice->payment_method,
                    'payment_date' => $invoice->payment_date,
                    'status' => $invoice->status,
                    'status_text' => $invoice->status_text,
                    'is_overdue' => $invoice->isOverdue(),
                    'items' => $invoice->items->map(function($item) {
                        return [
                            'id' => $item->id,
                            'name' => $item->name,
                            'quantity' => $item->quantity,
                            'price' => number_format($item->price, 2),
                            'total' => number_format($item->total, 2),
                        ];
                    }),
                    'created_at' => $invoice->created_at,
                    'updated_at' => $invoice->updated_at,
                ]
            ]
        ]);
    }

    /**
     * تحديث فاتورة
     * PUT /api/invoices/{id}
     */
    public function update(Request $request, $id): JsonResponse
    {
        $invoice = Invoice::find($id);
        
        if (!$invoice) {
            return response()->json([
                'success' => false,
                'message' => 'الفاتورة غير موجودة'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'sometimes|exists:users,id',
            'username' => 'sometimes|string|max:255',
            'due_date' => 'sometimes|date',
            'type' => 'sometimes|string|max:100',
            'amount' => 'sometimes|numeric|min:0',
            'description' => 'sometimes|string|max:500',
            'payment_method' => 'sometimes|string|max:100',
            'discount' => 'sometimes|numeric|min:0|max:100',
            'discount_value' => 'sometimes|numeric|min:0',
            'status' => 'sometimes|in:pending,approved,paid,cancelled',
            'invoice_items' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        try {
            $updateData = $request->only([
                'user_id', 'username', 'due_date', 'type', 'amount', 
                'description', 'payment_method', 'discount', 'discount_value', 'status'
            ]);

            // إعادة حساب الإجمالي إذا تم تحديث المبلغ أو الخصم
            if ($request->has('amount') || $request->has('discount') || $request->has('discount_value')) {
                $amount = $request->amount ?? $invoice->amount;
                $discount = $request->discount ?? $invoice->discount;
                $discountValue = $request->discount_value ?? ($amount * ($discount / 100));
                $updateData['total'] = $amount - $discountValue;
            }

            $invoice->update($updateData);

            // تحديث عناصر الفاتورة
            if ($request->has('invoice_items')) {
                $invoice->items()->delete();
                foreach ($request->invoice_items as $item) {
                    InvoiceItem::create([
                        'invoice_id' => $invoice->id,
                        'name' => $item['name'],
                        'quantity' => $item['quantity'],
                        'price' => $item['price'],
                        'total' => $item['quantity'] * $item['price'],
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الفاتورة بنجاح',
                'data' => [
                    'invoice' => [
                        'id' => $invoice->id,
                        'amount' => number_format($invoice->amount, 2),
                        'total' => number_format($invoice->total, 2),
                        'status' => $invoice->status,
                        'updated_at' => $invoice->updated_at,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الفاتورة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف فاتورة
     * DELETE /api/invoices/{id}
     */
    public function destroy($id): JsonResponse
    {
        $invoice = Invoice::find($id);
        
        if (!$invoice) {
            return response()->json([
                'success' => false,
                'message' => 'الفاتورة غير موجودة'
            ], 404);
        }

        DB::beginTransaction();
        try {
            $invoice->items()->delete();
            $invoice->delete();
            
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الفاتورة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * اعتماد فاتورة
     * POST /api/invoices/{id}/approve
     */
    public function approve($id): JsonResponse
    {
        $invoice = Invoice::find($id);
        
        if (!$invoice) {
            return response()->json([
                'success' => false,
                'message' => 'الفاتورة غير موجودة'
            ], 404);
        }

        $invoice->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم اعتماد الفاتورة بنجاح',
            'data' => [
                'invoice_id' => $invoice->id,
                'status' => $invoice->status,
                'approved_at' => $invoice->approved_at,
            ]
        ]);
    }

    /**
     * دفع فاتورة
     * POST /api/invoices/{id}/pay
     */
    public function pay(Request $request, $id): JsonResponse
    {
        $invoice = Invoice::find($id);
        
        if (!$invoice) {
            return response()->json([
                'success' => false,
                'message' => 'الفاتورة غير موجودة'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'payment_method' => 'sometimes|string|max:100',
            'payment_date' => 'sometimes|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $invoice->update([
            'status' => 'paid',
            'payment_date' => $request->payment_date ?? now(),
            'payment_method' => $request->payment_method ?? $invoice->payment_method,
            'paid_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم دفع الفاتورة بنجاح',
            'data' => [
                'invoice_id' => $invoice->id,
                'status' => $invoice->status,
                'payment_date' => $invoice->payment_date,
                'total_paid' => number_format($invoice->total, 2),
            ]
        ]);
    }
}
