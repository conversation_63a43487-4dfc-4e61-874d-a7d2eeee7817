<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use App\Models\Transaction;

class TransactionsController extends Controller
{
    /**
     * الحصول على جميع المعاملات
     * GET /api/transactions
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $type = $request->get('type'); // 'in' or 'out'
        $category = $request->get('category');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        
        $query = Transaction::with('user');
        
        if ($type) {
            $query->where('type', $type);
        }
        
        if ($category) {
            $query->where('category', $category);
        }
        
        if ($dateFrom) {
            $query->whereDate('transaction_date', '>=', $dateFrom);
        }
        
        if ($dateTo) {
            $query->whereDate('transaction_date', '<=', $dateTo);
        }
        
        $transactions = $query->orderBy('created_at', 'desc')->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page' => $transactions->lastPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
            ]
        ]);
    }

    /**
     * إنشاء معاملة جديدة
     * POST /api/transactions
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'type' => 'required|in:in,out',
            'category' => 'required|string|max:100',
            'description' => 'required|string|max:500',
            'transaction_date' => 'required|date',
            'currency' => 'sometimes|string|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $transaction = Transaction::create([
            'user_id' => $request->user_id,
            'amount' => $request->amount,
            'type' => $request->type,
            'category' => $request->category,
            'description' => $request->description,
            'transaction_date' => $request->transaction_date,
            'currency' => $request->currency ?? 'IQD',
            'status' => 'completed',
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء المعاملة بنجاح',
            'data' => [
                'transaction' => [
                    'id' => $transaction->id,
                    'user_id' => $transaction->user_id,
                    'amount' => number_format($transaction->amount, 2),
                    'type' => $transaction->type,
                    'type_text' => $transaction->type === 'in' ? 'دخل' : 'مصروف',
                    'category' => $transaction->category,
                    'description' => $transaction->description,
                    'transaction_date' => $transaction->transaction_date,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'created_at' => $transaction->created_at,
                ]
            ]
        ], 201);
    }

    /**
     * الحصول على معاملة بالمعرف
     * GET /api/transactions/{id}
     */
    public function show($id): JsonResponse
    {
        $transaction = Transaction::with('user')->find($id);
        
        if (!$transaction) {
            return response()->json([
                'success' => false,
                'message' => 'المعاملة غير موجودة'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'transaction' => [
                    'id' => $transaction->id,
                    'user_id' => $transaction->user_id,
                    'user_name' => $transaction->user->name ?? 'غير محدد',
                    'amount' => number_format($transaction->amount, 2),
                    'type' => $transaction->type,
                    'type_text' => $transaction->type === 'in' ? 'دخل' : 'مصروف',
                    'category' => $transaction->category,
                    'description' => $transaction->description,
                    'transaction_date' => $transaction->transaction_date,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'created_by' => $transaction->created_by,
                    'created_at' => $transaction->created_at,
                    'updated_at' => $transaction->updated_at,
                ]
            ]
        ]);
    }

    /**
     * تحديث معاملة
     * PUT /api/transactions/{id}
     */
    public function update(Request $request, $id): JsonResponse
    {
        $transaction = Transaction::find($id);
        
        if (!$transaction) {
            return response()->json([
                'success' => false,
                'message' => 'المعاملة غير موجودة'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'sometimes|exists:users,id',
            'amount' => 'sometimes|numeric|min:0',
            'type' => 'sometimes|in:in,out',
            'category' => 'sometimes|string|max:100',
            'description' => 'sometimes|string|max:500',
            'transaction_date' => 'sometimes|date',
            'currency' => 'sometimes|string|max:10',
            'status' => 'sometimes|in:pending,completed,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only([
            'user_id', 'amount', 'type', 'category', 'description', 
            'transaction_date', 'currency', 'status'
        ]);

        $transaction->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المعاملة بنجاح',
            'data' => [
                'transaction' => [
                    'id' => $transaction->id,
                    'user_id' => $transaction->user_id,
                    'amount' => number_format($transaction->amount, 2),
                    'type' => $transaction->type,
                    'type_text' => $transaction->type === 'in' ? 'دخل' : 'مصروف',
                    'category' => $transaction->category,
                    'description' => $transaction->description,
                    'transaction_date' => $transaction->transaction_date,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'updated_at' => $transaction->updated_at,
                ]
            ]
        ]);
    }

    /**
     * حذف معاملة
     * DELETE /api/transactions/{id}
     */
    public function destroy($id): JsonResponse
    {
        $transaction = Transaction::find($id);
        
        if (!$transaction) {
            return response()->json([
                'success' => false,
                'message' => 'المعاملة غير موجودة'
            ], 404);
        }

        $transaction->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المعاملة بنجاح'
        ]);
    }

    /**
     * الحصول على إحصائيات المعاملات
     * GET /api/transactions/stats
     */
    public function stats(): JsonResponse
    {
        $totalIncome = Transaction::where('type', 'in')->sum('amount');
        $totalExpenses = Transaction::where('type', 'out')->sum('amount');
        $netProfit = $totalIncome - $totalExpenses;
        
        $monthlyIncome = Transaction::where('type', 'in')
            ->whereMonth('transaction_date', now()->month)
            ->whereYear('transaction_date', now()->year)
            ->sum('amount');
            
        $monthlyExpenses = Transaction::where('type', 'out')
            ->whereMonth('transaction_date', now()->month)
            ->whereYear('transaction_date', now()->year)
            ->sum('amount');

        $todayIncome = Transaction::where('type', 'in')
            ->whereDate('transaction_date', today())
            ->sum('amount');
            
        $todayExpenses = Transaction::where('type', 'out')
            ->whereDate('transaction_date', today())
            ->sum('amount');

        // إحصائيات حسب الفئة
        $incomeByCategory = Transaction::where('type', 'in')
            ->selectRaw('category, SUM(amount) as total')
            ->groupBy('category')
            ->get()
            ->pluck('total', 'category');

        $expensesByCategory = Transaction::where('type', 'out')
            ->selectRaw('category, SUM(amount) as total')
            ->groupBy('category')
            ->get()
            ->pluck('total', 'category');

        return response()->json([
            'success' => true,
            'data' => [
                'totals' => [
                    'income' => number_format($totalIncome, 2),
                    'expenses' => number_format($totalExpenses, 2),
                    'net_profit' => number_format($netProfit, 2),
                ],
                'monthly' => [
                    'income' => number_format($monthlyIncome, 2),
                    'expenses' => number_format($monthlyExpenses, 2),
                    'net_profit' => number_format($monthlyIncome - $monthlyExpenses, 2),
                ],
                'today' => [
                    'income' => number_format($todayIncome, 2),
                    'expenses' => number_format($todayExpenses, 2),
                    'net_profit' => number_format($todayIncome - $todayExpenses, 2),
                ],
                'by_category' => [
                    'income' => $incomeByCategory,
                    'expenses' => $expensesByCategory,
                ]
            ]
        ]);
    }

    /**
     * تصدير المعاملات
     * GET /api/transactions/export
     */
    public function export(Request $request): JsonResponse
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());
        $type = $request->get('type');
        
        $query = Transaction::with('user')
            ->whereBetween('transaction_date', [$dateFrom, $dateTo]);
            
        if ($type) {
            $query->where('type', $type);
        }
        
        $transactions = $query->orderBy('transaction_date', 'desc')->get();
        
        $exportData = $transactions->map(function($transaction) {
            return [
                'ID' => $transaction->id,
                'المستخدم' => $transaction->user->name ?? 'غير محدد',
                'المبلغ' => $transaction->amount,
                'النوع' => $transaction->type === 'in' ? 'دخل' : 'مصروف',
                'الفئة' => $transaction->category,
                'الوصف' => $transaction->description,
                'التاريخ' => $transaction->transaction_date,
                'العملة' => $transaction->currency,
                'الحالة' => $transaction->status,
                'تاريخ الإنشاء' => $transaction->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'summary' => [
                'total_records' => $transactions->count(),
                'total_income' => number_format($transactions->where('type', 'in')->sum('amount'), 2),
                'total_expenses' => number_format($transactions->where('type', 'out')->sum('amount'), 2),
                'date_range' => [
                    'from' => $dateFrom,
                    'to' => $dateTo,
                ]
            ]
        ]);
    }
}
