<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

class UsersController extends Controller
{
    /**
     * الحصول على جميع المستخدمين
     * GET /api/users
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        
        $query = User::query();
        
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }
        
        $users = $query->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $users->items(),
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ]
        ]);
    }

    /**
     * إنشاء مستخدم جديد
     * POST /api/users
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'username' => 'sometimes|string|unique:users,username',
            'password' => 'required|string|min:6',
            'role' => 'sometimes|string|in:admin,manager,user',
            'phone' => 'sometimes|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username ?? $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role ?? 'user',
            'phone' => $request->phone,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء المستخدم بنجاح',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'username' => $user->username,
                    'role' => $user->role,
                    'phone' => $user->phone,
                    'created_at' => $user->created_at,
                ]
            ]
        ], 201);
    }

    /**
     * الحصول على مستخدم بالمعرف
     * GET /api/users/{id}
     */
    public function show($id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'username' => $user->username,
                    'role' => $user->role,
                    'phone' => $user->phone,
                    'status' => $user->status ?? 'active',
                    'profile_name' => $user->profile_name,
                    'profile_id' => $user->profile_id,
                    'balance' => $user->balance ?? '0.00',
                    'expiration' => $user->expiration,
                    'is_online' => $user->is_online ?? false,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                ]
            ]
        ]);
    }

    /**
     * تحديث مستخدم
     * PUT /api/users/{id}
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $id,
            'username' => 'sometimes|string|unique:users,username,' . $id,
            'password' => 'sometimes|string|min:6',
            'role' => 'sometimes|string|in:admin,manager,user',
            'phone' => 'sometimes|string|max:20',
            'status' => 'sometimes|string|in:active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only(['name', 'email', 'username', 'role', 'phone', 'status']);
        
        if ($request->has('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المستخدم بنجاح',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'username' => $user->username,
                    'role' => $user->role,
                    'phone' => $user->phone,
                    'status' => $user->status,
                    'updated_at' => $user->updated_at,
                ]
            ]
        ]);
    }

    /**
     * حذف مستخدم
     * DELETE /api/users/{id}
     */
    public function destroy($id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المستخدم بنجاح'
        ]);
    }

    /**
     * الحصول على المستخدمين المتصلين
     * GET /api/users/online
     */
    public function onlineUsers(): JsonResponse
    {
        $onlineUsers = User::where('is_online', true)->get();

        return response()->json([
            'success' => true,
            'data' => $onlineUsers->map(function($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'username' => $user->username,
                    'email' => $user->email,
                    'role' => $user->role,
                    'last_activity' => $user->last_activity ?? $user->updated_at,
                ];
            }),
            'count' => $onlineUsers->count()
        ]);
    }

    /**
     * تفعيل مستخدم
     * POST /api/users/{id}/activate
     */
    public function activate(Request $request, $id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'profile_id' => 'required|integer',
            'change_type' => 'required|string|in:immediate,scheduled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update([
            'status' => 'active',
            'profile_id' => $request->profile_id,
            'activated_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تفعيل المستخدم بنجاح',
            'data' => [
                'user_id' => $user->id,
                'status' => $user->status,
                'profile_id' => $user->profile_id,
                'activated_at' => $user->activated_at,
            ]
        ]);
    }

    /**
     * قطع اتصال المستخدم
     * POST /api/users/{id}/disconnect
     */
    public function disconnect($id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }

        $user->update([
            'is_online' => false,
            'last_activity' => now(),
        ]);

        // إلغاء جميع tokens المستخدم
        $user->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم قطع اتصال المستخدم بنجاح',
            'data' => [
                'user_id' => $user->id,
                'disconnected_at' => now(),
            ]
        ]);
    }

    /**
     * الحصول على إحصائيات المستخدم
     * GET /api/users/{id}/stats
     */
    public function getUserStats($id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'username' => $user->username,
                'profile_name' => $user->profile_name,
                'profile_id' => $user->profile_id,
                'balance' => $user->balance ?? '0.00',
                'status' => $user->status ?? 'active',
                'is_online' => $user->is_online ?? false,
                'created_at' => $user->created_at,
                'last_login' => $user->last_login ?? $user->updated_at,
                'total_sessions' => $user->total_sessions ?? 0,
                'total_usage' => $user->total_usage ?? '0 MB',
            ]
        ]);
    }
}
