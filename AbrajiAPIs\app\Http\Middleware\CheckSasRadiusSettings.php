<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\SasRadiusUrlService;
use Modules\Settings\Models\Setting;

class CheckSasRadiusSettings
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip check for settings routes
        if ($request->is('api/settings*')) {
            return $next($request);
        }

        // Skip check for authentication routes
        if ($request->is('api/auth*')) {
            return $next($request);
        }

        try {
            $authorization = $request->header('Authorization');
            
            if (!$authorization) {
                return response()->json([
                    'status' => 401,
                    'error' => 'Authorization header required'
                ], 401);
            }

            $adminId = SasRadiusUrlService::getAdminIdFromToken($authorization);
            $settings = Setting::forAdmin($adminId)->active()->first();

            // If no settings found, use default but warn user
            if (!$settings) {
                $request->headers->set('X-SAS-Radius-Warning', 'Using default SAS Radius settings. Please configure your settings.');
            }

            return $next($request);

        } catch (\Exception $e) {
            // If token is invalid, let other middleware handle it
            return $next($request);
        }
    }
}
