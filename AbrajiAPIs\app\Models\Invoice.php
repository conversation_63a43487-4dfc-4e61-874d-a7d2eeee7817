<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'username',
        'due_date',
        'type',
        'amount',
        'description',
        'created_by',
        'discount',
        'discount_value',
        'total',
        'payment_method',
        'payment_date',
        'status',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'discount' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'total' => 'decimal:2',
        'due_date' => 'date',
        'payment_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع المستخدم الذي أنشأ الفاتورة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع عناصر الفاتورة
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Scope للفواتير المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope للفواتير المدفوعة
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope للفواتير المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('status', '!=', 'paid');
    }

    /**
     * Scope للفواتير المعتمدة
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Accessor للمبلغ المنسق
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' د.ع';
    }

    /**
     * Accessor للإجمالي المنسق
     */
    public function getFormattedTotalAttribute()
    {
        return number_format($this->total, 2) . ' د.ع';
    }

    /**
     * Accessor لتاريخ الاستحقاق المنسق
     */
    public function getFormattedDueDateAttribute()
    {
        return $this->due_date->format('Y-m-d');
    }

    /**
     * Accessor لنص الحالة
     */
    public function getStatusTextAttribute()
    {
        $statuses = [
            'pending' => 'معلقة',
            'approved' => 'معتمدة',
            'paid' => 'مدفوعة',
            'cancelled' => 'ملغية',
            'overdue' => 'متأخرة',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Accessor للون الحالة
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'orange',
            'approved' => 'blue',
            'paid' => 'green',
            'cancelled' => 'red',
            'overdue' => 'red',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    /**
     * التحقق من كون الفاتورة مدفوعة
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * التحقق من كون الفاتورة معلقة
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * التحقق من كون الفاتورة معتمدة
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * التحقق من كون الفاتورة متأخرة
     */
    public function isOverdue(): bool
    {
        return $this->due_date->isPast() && !$this->isPaid();
    }

    /**
     * حساب الإجمالي
     */
    public function calculateTotal(): float
    {
        $subtotal = $this->amount;
        $discountAmount = $this->discount_value ?? ($subtotal * ($this->discount / 100));
        return $subtotal - $discountAmount;
    }

    /**
     * تحديث الإجمالي
     */
    public function updateTotal(): void
    {
        $this->total = $this->calculateTotal();
        $this->save();
    }
}
