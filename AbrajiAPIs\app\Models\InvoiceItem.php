<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'name',
        'description',
        'quantity',
        'price',
        'total',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'total' => 'decimal:2',
    ];

    /**
     * العلاقة مع الفاتورة
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Accessor للسعر المنسق
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2) . ' د.ع';
    }

    /**
     * Accessor للإجمالي المنسق
     */
    public function getFormattedTotalAttribute()
    {
        return number_format($this->total, 2) . ' د.ع';
    }

    /**
     * حساب الإجمالي
     */
    public function calculateTotal(): float
    {
        return $this->quantity * $this->price;
    }

    /**
     * تحديث الإجمالي
     */
    public function updateTotal(): void
    {
        $this->total = $this->calculateTotal();
        $this->save();
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total = $item->calculateTotal();
        });
    }
}
