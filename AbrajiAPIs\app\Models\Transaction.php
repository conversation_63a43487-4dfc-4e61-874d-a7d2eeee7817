<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'amount',
        'type',
        'category',
        'description',
        'transaction_date',
        'currency',
        'status',
        'created_by',
        'reference_number',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع المستخدم الذي أنشأ المعاملة
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope للدخل
     */
    public function scopeIncome($query)
    {
        return $query->where('type', 'in');
    }

    /**
     * Scope للمصروفات
     */
    public function scopeExpenses($query)
    {
        return $query->where('type', 'out');
    }

    /**
     * Scope للمعاملات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope للمعاملات حسب الفئة
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope للمعاملات في فترة زمنية
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    /**
     * Accessor للمبلغ المنسق
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    /**
     * Accessor لنص النوع
     */
    public function getTypeTextAttribute()
    {
        return $this->type === 'in' ? 'دخل' : 'مصروف';
    }

    /**
     * Accessor لنص الحالة
     */
    public function getStatusTextAttribute()
    {
        $statuses = [
            'pending' => 'معلق',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Accessor للون النوع
     */
    public function getTypeColorAttribute()
    {
        return $this->type === 'in' ? 'green' : 'red';
    }

    /**
     * التحقق من كون المعاملة دخل
     */
    public function isIncome(): bool
    {
        return $this->type === 'in';
    }

    /**
     * التحقق من كون المعاملة مصروف
     */
    public function isExpense(): bool
    {
        return $this->type === 'out';
    }

    /**
     * التحقق من كون المعاملة مكتملة
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * التحقق من كون المعاملة معلقة
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * التحقق من كون المعاملة ملغية
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }
}
