<?php

namespace App\Services;

use Modules\Settings\Models\Setting;

class SasRadiusUrlService
{
    /**
     * Get SAS Radius URL for a specific admin
     */
    public static function getUrlForAdmin($adminId)
    {
        $settings = Setting::where('admin_id', $adminId)
            ->where('is_active', true)
            ->first();

        if ($settings) {
            return $settings->full_url;
        }

        return config('app.api_domain');
    }

    /**
     * Get admin ID from JWT token
     */
    public static function getAdminIdFromToken($token)
    {
        try {
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                throw new \Exception('Invalid token format');
            }

            $payload = base64_decode($tokenParts[1]);
            $decoded = json_decode($payload);

            if (isset($decoded->sub)) {
                return $decoded->sub;
            } else {
                throw new \Exception('Admin ID not found in token');
            }
        } catch (\Exception $e) {
            throw new \Exception('Token validation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get SAS Radius URL from request authorization header
     */
    public static function getUrlFromRequest($request)
    {
        try {
            $authorization = $request->header('Authorization');
            $adminId = self::getAdminIdFromToken($authorization);
            return self::getUrlForAdmin($adminId);
        } catch (\Exception $e) {
            return config('app.api_domain');
        }
    }
}
