#!/bin/bash

# AbrajiAPIs Deployment Script
# This script deploys the project to VPS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_header "AbrajiAPIs Deployment Script"

# Get deployment details
read -p "Enter VPS IP address: " VPS_IP
read -p "Enter VPS username (default: root): " VPS_USER
VPS_USER=${VPS_USER:-root}
read -p "Enter domain name: " DOMAIN_NAME
read -s -p "Enter database password for abrajiapis user: " DB_PASSWORD
echo ""
read -s -p "Enter MySQL root password: " MYSQL_ROOT_PASSWORD
echo ""

PROJECT_DIR="/home/<USER>/AbrajiAPIs"
LOCAL_PROJECT_DIR="."

print_status "Deployment configuration:"
print_status "- VPS IP: $VPS_IP"
print_status "- VPS User: $VPS_USER"
print_status "- Domain: $DOMAIN_NAME"
print_status "- Remote directory: $PROJECT_DIR"

# Check if local project exists
if [ ! -f "artisan" ]; then
    print_error "Laravel project not found in current directory"
    print_error "Please run this script from your AbrajiAPIs project root"
    exit 1
fi

print_status "Local project found"

# Create deployment package
print_status "Creating deployment package..."
TEMP_DIR=$(mktemp -d)
PACKAGE_NAME="abrajiapis-$(date +%Y%m%d_%H%M%S).tar.gz"

# Copy project files (excluding unnecessary files)
rsync -av --exclude-from=- . "$TEMP_DIR/AbrajiAPIs/" << 'EOF'
.git/
.env
node_modules/
vendor/
storage/logs/*
storage/framework/cache/*
storage/framework/sessions/*
storage/framework/views/*
bootstrap/cache/*
.DS_Store
Thumbs.db
*.log
EOF

# Create .env file for production
cat > "$TEMP_DIR/AbrajiAPIs/.env" << EOF
APP_NAME=AbrajiAPIs
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://$DOMAIN_NAME

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=abrajiapis
DB_USERNAME=abrajiapis
DB_PASSWORD=$DB_PASSWORD

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# SAS Radius Configuration
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost
EOF

# Create package
cd "$TEMP_DIR"
tar -czf "$PACKAGE_NAME" AbrajiAPIs/
cd - > /dev/null

print_status "Package created: $TEMP_DIR/$PACKAGE_NAME"

# Upload package to VPS
print_status "Uploading package to VPS..."
scp "$TEMP_DIR/$PACKAGE_NAME" "$VPS_USER@$VPS_IP:/tmp/"

# Create deployment script for VPS
cat > "$TEMP_DIR/deploy_on_vps.sh" << 'EOF'
#!/bin/bash

set -e

PACKAGE_NAME="$1"
PROJECT_DIR="/home/<USER>/AbrajiAPIs"

echo "Starting deployment on VPS..."

# Backup current installation if exists
if [ -d "$PROJECT_DIR" ]; then
    echo "Creating backup of current installation..."
    sudo -u abrajiapis tar -czf "/home/<USER>/backups/pre-deploy-$(date +%Y%m%d_%H%M%S).tar.gz" -C "/home/<USER>" "AbrajiAPIs"
fi

# Extract new version
echo "Extracting new version..."
cd /tmp
tar -xzf "$PACKAGE_NAME"

# Stop services temporarily
echo "Stopping services..."
systemctl stop php8.2-fpm

# Move old installation and install new one
if [ -d "$PROJECT_DIR" ]; then
    sudo rm -rf "${PROJECT_DIR}.old"
    sudo mv "$PROJECT_DIR" "${PROJECT_DIR}.old"
fi

sudo mv "/tmp/AbrajiAPIs" "$PROJECT_DIR"
sudo chown -R abrajiapis:www-data "$PROJECT_DIR"

# Switch to project directory as abrajiapis user
sudo -u abrajiapis bash << 'DEPLOY_SCRIPT'
cd /home/<USER>/AbrajiAPIs

# Install dependencies
composer install --no-dev --optimize-autoloader --no-interaction

# Generate application key if not set
if ! grep -q "APP_KEY=base64:" .env; then
    php artisan key:generate --force
fi

# Run migrations
php artisan migrate --force

# Clear and cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link

echo "Application deployed successfully"
DEPLOY_SCRIPT

# Set proper permissions
echo "Setting permissions..."
sudo find "$PROJECT_DIR" -type f -exec chmod 644 {} \;
sudo find "$PROJECT_DIR" -type d -exec chmod 755 {} \;
sudo chmod -R 775 "$PROJECT_DIR/storage"
sudo chmod -R 775 "$PROJECT_DIR/bootstrap/cache"

# Start services
echo "Starting services..."
systemctl start php8.2-fpm
systemctl reload nginx

# Cleanup
rm -f "/tmp/$PACKAGE_NAME"
rm -rf "/tmp/AbrajiAPIs"

echo "Deployment completed successfully!"
echo "Site is available at: https://$(hostname -f)"
EOF

chmod +x "$TEMP_DIR/deploy_on_vps.sh"

# Upload deployment script
print_status "Uploading deployment script..."
scp "$TEMP_DIR/deploy_on_vps.sh" "$VPS_USER@$VPS_IP:/tmp/"

# Execute deployment on VPS
print_status "Executing deployment on VPS..."
ssh "$VPS_USER@$VPS_IP" "bash /tmp/deploy_on_vps.sh $PACKAGE_NAME"

# Cleanup local temp files
rm -rf "$TEMP_DIR"

print_header "Deployment Completed Successfully!"
print_status ""
print_status "Your AbrajiAPIs application has been deployed to:"
print_status "🌐 https://$DOMAIN_NAME"
print_status ""
print_status "Next steps:"
print_status "1. Test the application: curl -I https://$DOMAIN_NAME"
print_status "2. Configure SAS Radius settings via API"
print_status "3. Test API endpoints"
print_status ""
print_status "Useful commands on VPS:"
print_status "- View logs: sudo tail -f $PROJECT_DIR/storage/logs/laravel.log"
print_status "- Run artisan commands: sudo -u abrajiapis php $PROJECT_DIR/artisan [command]"
print_status "- Restart services: sudo systemctl restart php8.2-fpm nginx"
print_status ""
print_warning "Don't forget to:"
print_warning "- Configure your SAS Radius settings"
print_warning "- Test all API endpoints"
print_warning "- Set up monitoring and alerts"
print_status ""
print_header "Happy coding! 🚀"

