#!/bin/bash

# MySQL Fix Script for AbrajiAPIs
# Run this script to fix MySQL authentication issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=========================================="
echo "  MySQL Fix Script for AbrajiAPIs"
echo "=========================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Get passwords
read -s -p "Enter new MySQL root password: " MYSQL_ROOT_PASSWORD
echo ""
read -s -p "Enter password for abrajiapis database user: " DB_PASSWORD
echo ""

print_status "Starting MySQL fix process..."

# Method 1: Try to reset MySQL root password
print_status "Attempting to reset MySQL root password..."

# Stop MySQL
print_status "Stopping MySQL service..."
systemctl stop mysql

# Start MySQL in safe mode
print_status "Starting MySQL in safe mode..."
mysqld_safe --skip-grant-tables --skip-networking &
SAFE_PID=$!

# Wait for MySQL to start
sleep 10

# Reset root password
print_status "Resetting root password..."
mysql -u root << EOF
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
FLUSH PRIVILEGES;
EXIT;
EOF

# Kill safe mode
print_status "Stopping safe mode..."
kill $SAFE_PID 2>/dev/null || true
pkill mysqld_safe 2>/dev/null || true
pkill mysqld 2>/dev/null || true

# Wait a moment
sleep 5

# Start MySQL normally
print_status "Starting MySQL service..."
systemctl start mysql
systemctl enable mysql

# Wait for MySQL to be ready
sleep 5

# Test connection
print_status "Testing MySQL connection..."
if mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SELECT 1;" &>/dev/null; then
    print_status "✅ MySQL root connection successful!"
else
    print_error "❌ MySQL root connection failed. Trying alternative method..."
    
    # Alternative method: Reinstall MySQL
    print_warning "Reinstalling MySQL..."
    
    # Remove MySQL completely
    systemctl stop mysql 2>/dev/null || true
    apt remove --purge mysql-server mysql-client mysql-common mysql-server-core-* mysql-client-core-* -y
    apt autoremove -y
    apt autoclean
    
    # Remove data directories
    rm -rf /var/lib/mysql
    rm -rf /etc/mysql
    
    # Reinstall MySQL
    apt update
    apt install -y mysql-server
    
    # Start MySQL
    systemctl start mysql
    systemctl enable mysql
    
    # Set root password
    mysql -u root << EOF
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
FLUSH PRIVILEGES;
EXIT;
EOF
    
    print_status "✅ MySQL reinstalled successfully!"
fi

# Create database and user
print_status "Creating database and user..."
mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
CREATE DATABASE IF NOT EXISTS abrajiapis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'abrajiapis'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON abrajiapis.* TO 'abrajiapis'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

# Test abrajiapis user connection
print_status "Testing abrajiapis user connection..."
if mysql -u abrajiapis -p$DB_PASSWORD -e "USE abrajiapis; SELECT 1;" &>/dev/null; then
    print_status "✅ abrajiapis user connection successful!"
else
    print_error "❌ abrajiapis user connection failed!"
    exit 1
fi

# Secure MySQL installation
print_status "Securing MySQL installation..."
mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';
FLUSH PRIVILEGES;
EXIT;
EOF

# Create MySQL configuration for better performance
print_status "Optimizing MySQL configuration..."
cat > /etc/mysql/mysql.conf.d/abrajiapis.cnf << 'EOF'
[mysqld]
# Basic settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Query cache
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# Connection settings
max_connections = 100
connect_timeout = 10
wait_timeout = 600
max_allowed_packet = 64M

# Logging
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
EOF

# Restart MySQL to apply configuration
print_status "Restarting MySQL to apply configuration..."
systemctl restart mysql

# Final test
print_status "Performing final tests..."
if mysql -u root -p$MYSQL_ROOT_PASSWORD -e "SHOW DATABASES;" | grep -q abrajiapis; then
    print_status "✅ Database 'abrajiapis' exists"
else
    print_error "❌ Database 'abrajiapis' not found"
    exit 1
fi

if mysql -u abrajiapis -p$DB_PASSWORD abrajiapis -e "SELECT 1;" &>/dev/null; then
    print_status "✅ User 'abrajiapis' can access database"
else
    print_error "❌ User 'abrajiapis' cannot access database"
    exit 1
fi

# Create .my.cnf files for easy access
print_status "Creating MySQL configuration files..."

# Root user config
cat > /root/.my.cnf << EOF
[client]
user = root
password = $MYSQL_ROOT_PASSWORD
EOF
chmod 600 /root/.my.cnf

# Abrajiapis user config
mkdir -p /home/<USER>
cat > /home/<USER>/.my.cnf << EOF
[client]
user = abrajiapis
password = $DB_PASSWORD
database = abrajiapis
EOF
chown abrajiapis:abrajiapis /home/<USER>/.my.cnf 2>/dev/null || true
chmod 600 /home/<USER>/.my.cnf

print_status "=========================================="
print_status "✅ MySQL fix completed successfully!"
print_status "=========================================="
print_status ""
print_status "Database Details:"
print_status "- Database: abrajiapis"
print_status "- Username: abrajiapis"
print_status "- Password: [saved in /home/<USER>/.my.cnf]"
print_status ""
print_status "Test commands:"
print_status "- Root access: mysql -u root -p"
print_status "- User access: mysql -u abrajiapis -p abrajiapis"
print_status "- Or simply: mysql (from /home/<USER>/)"
print_status ""
print_status "Next steps:"
print_status "1. Update your .env file with database credentials"
print_status "2. Run: php artisan migrate"
print_status "3. Continue with your deployment"
print_status "=========================================="
