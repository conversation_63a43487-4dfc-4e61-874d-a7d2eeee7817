# AbrajiAPIs Flutter App

تطبيق Flutter متكامل للتفاعل مع نظام AbrajiAPIs لإدارة الفواتير والمعاملات المالية.

## 🚀 المميزات

### 🔐 المصادقة والأمان
- تسجيل الدخول والخروج
- إنشاء حساب جديد
- إعادة تعيين كلمة المرور
- حفظ بيانات المصادقة بشكل آمن
- إدارة الجلسات تلقائياً

### 📊 لوحة التحكم
- إحصائيات شاملة للأعمال
- رسوم بيانية تفاعلية
- ملخص المبيعات والفواتير
- النشاطات الأخيرة

### 🧾 إدارة الفواتير
- عرض قائمة الفواتير
- إنشاء فواتير جديدة
- تعديل الفواتير الموجودة
- إضافة مدفوعات للفواتير
- تصدير الفواتير PDF

### 💰 إدارة المعاملات
- عرض جميع المعاملات
- تفاصيل كل معاملة
- فلترة وبحث المعاملات
- إحصائيات المعاملات

### 👛 إدارة المحفظة
- رصيد المحفظة الحالي
- تاريخ المعاملات
- إضافة وسحب الأموال
- تحويل الأموال

### ⚙️ الإعدادات
- إدارة الملف الشخصي
- تغيير كلمة المرور
- إعدادات التطبيق
- إعدادات الإشعارات

## 🛠️ التقنيات المستخدمة

### Frontend (Flutter)
- **Flutter 3.x** - إطار العمل الرئيسي
- **Riverpod** - إدارة الحالة
- **Go Router** - التنقل والتوجيه
- **Dio** - طلبات HTTP
- **Hive** - قاعدة البيانات المحلية
- **FL Chart** - الرسوم البيانية
- **Font Awesome** - الأيقونات

### Backend Integration
- **Laravel APIs** - التكامل مع AbrajiAPIs
- **JWT Authentication** - المصادقة الآمنة
- **RESTful APIs** - واجهات برمجة التطبيقات

## 📱 متطلبات النظام

### Android
- Android 5.0 (API level 21) أو أحدث
- 2GB RAM أو أكثر
- 100MB مساحة تخزين

### iOS
- iOS 11.0 أو أحدث
- iPhone 6s أو أحدث
- 100MB مساحة تخزين

## 🔧 التثبيت والإعداد

### 1. متطلبات التطوير
```bash
# تثبيت Flutter (الإصدار 3.0 أو أحدث)
flutter --version

# التحقق من البيئة
flutter doctor
```

### 2. إنشاء المشروع
```bash
# إنشاء مشروع Flutter جديد
flutter create abraji_apis_app
cd abraji_apis_app

# نسخ الملفات المنشأة إلى مجلد المشروع
# انسخ جميع الملفات من flutter_app/ إلى مجلد المشروع
```

### 3. تثبيت التبعيات
```bash
# تثبيت التبعيات
flutter pub get

# في حالة وجود مشاكل، قم بتنظيف المشروع
flutter clean
flutter pub get
```

### 4. إعداد التكوين
قم بتحديث ملف `lib/core/config/app_config.dart`:
```dart
static const String baseUrl = 'https://abraji.com/api'; // عنوان API الخاص بك
```

### 5. إصلاح الأخطاء المحتملة
إذا واجهت أخطاء في الاستيراد، تأكد من:

1. **إضافة الاستيرادات المفقودة:**
```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
```

2. **التحقق من إصدار Flutter:**
```bash
flutter upgrade
```

3. **إعادة تثبيت التبعيات:**
```bash
flutter pub deps
flutter pub get
```

### 6. تشغيل التطبيق
```bash
# تشغيل على Android
flutter run

# تشغيل على iOS (macOS فقط)
flutter run -d ios

# تشغيل في وضع الإنتاج
flutter run --release
```

### 7. حل المشاكل الشائعة

#### مشكلة: أخطاء في الاستيراد
```bash
# حذف مجلد build
rm -rf build/
flutter clean
flutter pub get
```

#### مشكلة: أخطاء في التبعيات
```bash
# تحديث التبعيات
flutter pub upgrade
```

#### مشكلة: أخطاء في التوجيه
تأكد من أن جميع الشاشات موجودة في المسارات الصحيحة.

## 🏗️ هيكل المشروع

```
lib/
├── core/                   # الملفات الأساسية
│   ├── config/            # إعدادات التطبيق
│   ├── services/          # الخدمات (API, Storage)
│   ├── theme/             # تصميم التطبيق
│   └── router/            # التوجيه والتنقل
├── features/              # ميزات التطبيق
│   ├── auth/              # المصادقة
│   ├── dashboard/         # لوحة التحكم
│   ├── invoice/           # الفواتير
│   ├── transaction/       # المعاملات
│   ├── wallet/            # المحفظة
│   └── settings/          # الإعدادات
├── shared/                # المكونات المشتركة
│   ├── widgets/           # الويدجت المشتركة
│   ├── models/            # النماذج المشتركة
│   └── utils/             # الأدوات المساعدة
└── main.dart              # نقطة البداية
```

## 🔗 تكوين API

### إعداد عنوان الخادم
```dart
// lib/core/config/app_config.dart
class AppConfig {
  static const String baseUrl = 'https://abraji.com/api';
  // باقي الإعدادات...
}
```

### إعداد المصادقة
```dart
// تسجيل الدخول
final success = await ref.read(authProvider.notifier).login(
  email: '<EMAIL>',
  password: 'password',
);

// الحصول على المستخدم الحالي
final user = ref.watch(authProvider).user;
```

## 📊 استخدام التطبيق

### 1. تسجيل الدخول
- أدخل البريد الإلكتروني وكلمة المرور
- اختر "تذكرني" للبقاء مسجلاً
- استخدم "نسيت كلمة المرور" عند الحاجة

### 2. لوحة التحكم
- عرض الإحصائيات الرئيسية
- متابعة النشاطات الأخيرة
- الوصول السريع للميزات

### 3. إدارة الفواتير
- إنشاء فاتورة جديدة
- إضافة عناصر للفاتورة
- إرسال الفاتورة للعميل
- تتبع حالة الدفع

### 4. إدارة المحفظة
- عرض الرصيد الحالي
- إضافة أموال للمحفظة
- سحب الأموال
- عرض تاريخ المعاملات

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/

# اختبارات الأداء
flutter test --coverage
```

### اختبار على الأجهزة
```bash
# Android
flutter run -d android

# iOS
flutter run -d ios

# Web (للتطوير)
flutter run -d chrome
```

## 📦 البناء للإنتاج

### Android APK
```bash
flutter build apk --release
```

### Android App Bundle
```bash
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 🔒 الأمان

### حماية البيانات
- تشفير البيانات المحلية
- استخدام HTTPS للاتصالات
- حماية JWT tokens
- تنظيف البيانات عند تسجيل الخروج

### أفضل الممارسات
- فحص صحة المدخلات
- معالجة الأخطاء بشكل آمن
- عدم تخزين كلمات المرور
- استخدام التخزين الآمن

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال بالخادم**
   - تحقق من عنوان API
   - تأكد من اتصال الإنترنت

2. **فشل في تسجيل الدخول**
   - تحقق من بيانات المصادقة
   - تأكد من صحة عنوان API

3. **مشاكل في البناء**
   - نظف المشروع: `flutter clean`
   - أعد تثبيت التبعيات: `flutter pub get`

## 📞 الدعم والمساعدة

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://abraji.com
- **التوثيق**: https://docs.abraji.com

### المساهمة
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- فريق Flutter لإطار العمل الرائع
- مجتمع Dart و Flutter
- جميع المساهمين في المكتبات المستخدمة

---

**تم تطوير هذا التطبيق بواسطة فريق AbrajiAPIs**
