class AppConfig {
  // API Configuration
  static const String baseUrl = 'https://abraji.com/api';
  static const String apiVersion = 'v1';

  // Endpoints
  static const String authEndpoint = '/auth/login';
  static const String usersEndpoint = '/users';
  static const String dashboardEndpoint = '/dashboard';
  static const String invoicesEndpoint = '/invoices';
  static const String transactionsEndpoint = '/transactions';
  static const String walletEndpoint = '/wallet';
  static const String debtsEndpoint = '/debts';
  static const String cardsEndpoint = '/card';
  static const String settingsEndpoint = '/settings';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';

  // App Settings
  static const int requestTimeout = 30000; // 30 seconds
  static const int connectTimeout = 15000; // 15 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Pagination
  static const int defaultPageSize = 15;
  static const int maxPageSize = 100;

  // Cache Settings
  static const Duration cacheExpiry = Duration(minutes: 30);
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);

  // UI Settings
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  static const double defaultRadius = 12.0;
  static const double smallRadius = 8.0;
  static const double largeRadius = 16.0;

  // Colors
  static const int primaryColorValue = 0xFF2196F3;
  static const int secondaryColorValue = 0xFF03DAC6;
  static const int errorColorValue = 0xFFB00020;
  static const int successColorValue = 0xFF4CAF50;
  static const int warningColorValue = 0xFFFF9800;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'txt',
  ];

  // Environment
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');
  static const bool enableLogging = !isProduction;

  // Features
  static const bool enableBiometrics = true;
  static const bool enablePushNotifications = true;
  static const bool enableAnalytics = isProduction;

  // URLs
  static String get fullApiUrl => baseUrl;
  static String get authUrl => '$fullApiUrl$authEndpoint';
  static String get usersUrl => '$fullApiUrl$usersEndpoint';
  static String get dashboardUrl => '$fullApiUrl$dashboardEndpoint';
  static String get invoicesUrl => '$fullApiUrl$invoicesEndpoint';
  static String get transactionsUrl => '$fullApiUrl$transactionsEndpoint';
  static String get walletUrl => '$fullApiUrl$walletEndpoint';
  static String get debtsUrl => '$fullApiUrl$debtsEndpoint';
  static String get cardsUrl => '$fullApiUrl$cardsEndpoint';
  static String get settingsUrl => '$fullApiUrl$settingsEndpoint';
}
