import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../config/app_config.dart';
import 'storage_service.dart';

class ApiResponse<T> {
  final bool isSuccess;
  final T? data;
  final String? error;
  final int? statusCode;

  ApiResponse({
    required this.isSuccess,
    this.data,
    this.error,
    this.statusCode,
  });

  factory ApiResponse.success(T data, {int? statusCode}) {
    return ApiResponse<T>(
      isSuccess: true,
      data: data,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error(String error, {int? statusCode}) {
    return ApiResponse<T>(
      isSuccess: false,
      error: error,
      statusCode: statusCode,
    );
  }
}

class ApiService {
  static String? _authToken;
  static const Duration _timeout = Duration(seconds: 30);

  static void init() {
    _loadAuthToken();
  }

  static Future<void> _loadAuthToken() async {
    _authToken = await getAuthToken();
  }

  static Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (_authToken != null) 'Authorization': 'Bearer $_authToken',
  };

  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final url = Uri.parse('${AppConfig.baseUrl}$endpoint');
      final response = await http.get(
        url,
        headers: {..._defaultHeaders, ...?headers},
      ).timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final url = Uri.parse('${AppConfig.baseUrl}$endpoint');
      final response = await http.post(
        url,
        headers: {..._defaultHeaders, ...?headers},
        body: data != null ? jsonEncode(data) : null,
      ).timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final url = Uri.parse('${AppConfig.baseUrl}$endpoint');
      final response = await http.put(
        url,
        headers: {..._defaultHeaders, ...?headers},
        body: data != null ? jsonEncode(data) : null,
      ).timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final url = Uri.parse('${AppConfig.baseUrl}$endpoint');
      final response = await http.delete(
        url,
        headers: {..._defaultHeaders, ...?headers},
      ).timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  static ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    try {
      final statusCode = response.statusCode;
      
      if (statusCode >= 200 && statusCode < 300) {
        if (response.body.isEmpty) {
          return ApiResponse.success(null as T, statusCode: statusCode);
        }

        final jsonData = jsonDecode(response.body);
        
        if (fromJson != null && jsonData is Map<String, dynamic>) {
          final data = fromJson(jsonData);
          return ApiResponse.success(data, statusCode: statusCode);
        } else {
          return ApiResponse.success(jsonData as T, statusCode: statusCode);
        }
      } else {
        String errorMessage = 'خطأ في الخادم';
        
        try {
          final errorData = jsonDecode(response.body);
          if (errorData is Map<String, dynamic>) {
            errorMessage = errorData['message'] ?? 
                          errorData['error'] ?? 
                          'خطأ غير معروف';
          }
        } catch (e) {
          errorMessage = 'خطأ في معالجة الاستجابة';
        }
        
        return ApiResponse.error(errorMessage, statusCode: statusCode);
      }
    } catch (e) {
      return ApiResponse.error('خطأ في معالجة الاستجابة: ${e.toString()}');
    }
  }

  static String _handleError(dynamic error) {
    if (error is SocketException) {
      return 'لا يوجد اتصال بالإنترنت';
    } else if (error is HttpException) {
      return 'خطأ في الاتصال بالخادم';
    } else if (error is FormatException) {
      return 'خطأ في تنسيق البيانات';
    } else {
      return 'حدث خطأ غير متوقع: ${error.toString()}';
    }
  }

  // Auth token management
  static Future<String?> getAuthToken() async {
    try {
      _authToken = await StorageService.getString(AppConfig.tokenKey);
      return _authToken;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting auth token: $e');
      }
      return null;
    }
  }

  static Future<void> setAuthToken(String token) async {
    try {
      _authToken = token;
      await StorageService.setString(AppConfig.tokenKey, token);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting auth token: $e');
      }
    }
  }

  static Future<void> clearAuthToken() async {
    try {
      _authToken = null;
      await StorageService.remove(AppConfig.tokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing auth token: $e');
      }
    }
  }

  // Upload file method (simplified)
  static Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, String>? additionalFields,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final url = Uri.parse('${AppConfig.baseUrl}$endpoint');
      final request = http.MultipartRequest('POST', url);
      
      // Add headers
      request.headers.addAll({..._defaultHeaders, ...?headers});
      
      // Add file
      request.files.add(await http.MultipartFile.fromPath(fieldName, file.path));
      
      // Add additional fields
      if (additionalFields != null) {
        request.fields.addAll(additionalFields);
      }
      
      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);
      
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
  }

  // Utility method to check if user is authenticated
  static bool get isAuthenticated => _authToken != null;

  // Method to refresh token (placeholder)
  static Future<bool> refreshToken() async {
    try {
      // Implement token refresh logic here
      // This is a placeholder implementation
      await Future.delayed(const Duration(milliseconds: 500));
      return true;
    } catch (e) {
      return false;
    }
  }
}
