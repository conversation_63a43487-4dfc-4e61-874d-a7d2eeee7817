import 'dart:convert';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StorageService {
  static late Box _box;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static Future<void> init() async {
    _box = await Hive.openBox('app_storage');
  }

  // Regular Storage (Hive)
  static Future<void> setString(String key, String value) async {
    await _box.put(key, value);
  }

  static Future<String?> getString(String key) async {
    return _box.get(key);
  }

  static Future<void> setInt(String key, int value) async {
    await _box.put(key, value);
  }

  static Future<int?> getInt(String key) async {
    return _box.get(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _box.put(key, value);
  }

  static Future<bool?> getBool(String key) async {
    return _box.get(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _box.put(key, value);
  }

  static Future<double?> getDouble(String key) async {
    return _box.get(key);
  }

  static Future<void> setObject(String key, Map<String, dynamic> value) async {
    await _box.put(key, jsonEncode(value));
  }

  static Future<Map<String, dynamic>?> getObject(String key) async {
    final value = _box.get(key);
    if (value != null) {
      try {
        return jsonDecode(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<void> setList(String key, List<dynamic> value) async {
    await _box.put(key, jsonEncode(value));
  }

  static Future<List<dynamic>?> getList(String key) async {
    final value = _box.get(key);
    if (value != null) {
      try {
        return jsonDecode(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<void> remove(String key) async {
    await _box.delete(key);
  }

  static Future<void> clear() async {
    await _box.clear();
  }

  static bool containsKey(String key) {
    return _box.containsKey(key);
  }

  static List<String> getAllKeys() {
    return _box.keys.cast<String>().toList();
  }

  // Secure Storage
  static Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  static Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }

  static Future<void> setSecureObject(String key, Map<String, dynamic> value) async {
    await _secureStorage.write(key: key, value: jsonEncode(value));
  }

  static Future<Map<String, dynamic>?> getSecureObject(String key) async {
    final value = await _secureStorage.read(key: key);
    if (value != null) {
      try {
        return jsonDecode(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<void> removeSecure(String key) async {
    await _secureStorage.delete(key: key);
  }

  static Future<void> clearSecure() async {
    await _secureStorage.deleteAll();
  }

  static Future<bool> containsSecureKey(String key) async {
    return await _secureStorage.containsKey(key: key);
  }

  static Future<Map<String, String>> getAllSecure() async {
    return await _secureStorage.readAll();
  }

  // Cache Management
  static Future<void> setCacheWithExpiry(
    String key,
    dynamic value,
    Duration expiry,
  ) async {
    final cacheData = {
      'value': value,
      'expiry': DateTime.now().add(expiry).millisecondsSinceEpoch,
    };
    await setObject('cache_$key', cacheData);
  }

  static Future<T?> getCacheWithExpiry<T>(String key) async {
    final cacheData = await getObject('cache_$key');
    if (cacheData != null) {
      final expiry = cacheData['expiry'] as int?;
      if (expiry != null && DateTime.now().millisecondsSinceEpoch < expiry) {
        return cacheData['value'] as T?;
      } else {
        // Cache expired, remove it
        await remove('cache_$key');
      }
    }
    return null;
  }

  static Future<void> clearExpiredCache() async {
    final keys = getAllKeys();
    final now = DateTime.now().millisecondsSinceEpoch;
    
    for (final key in keys) {
      if (key.startsWith('cache_')) {
        final cacheData = await getObject(key);
        if (cacheData != null) {
          final expiry = cacheData['expiry'] as int?;
          if (expiry != null && now >= expiry) {
            await remove(key);
          }
        }
      }
    }
  }

  // User Preferences
  static Future<void> setUserPreference(String key, dynamic value) async {
    await setObject('pref_$key', {'value': value});
  }

  static Future<T?> getUserPreference<T>(String key) async {
    final data = await getObject('pref_$key');
    return data?['value'] as T?;
  }

  static Future<void> removeUserPreference(String key) async {
    await remove('pref_$key');
  }

  static Future<void> clearUserPreferences() async {
    final keys = getAllKeys();
    for (final key in keys) {
      if (key.startsWith('pref_')) {
        await remove(key);
      }
    }
  }
}
