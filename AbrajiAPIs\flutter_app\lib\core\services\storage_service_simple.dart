import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static SharedPreferences get _instance {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // String operations
  static Future<void> setString(String key, String value) async {
    await _instance.setString(key, value);
  }

  static String? getString(String key) {
    return _instance.getString(key);
  }

  // Integer operations
  static Future<void> setInt(String key, int value) async {
    await _instance.setInt(key, value);
  }

  static int? getInt(String key) {
    return _instance.getInt(key);
  }

  // Boolean operations
  static Future<void> setBool(String key, bool value) async {
    await _instance.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _instance.getBool(key);
  }

  // Double operations
  static Future<void> setDouble(String key, double value) async {
    await _instance.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _instance.getDouble(key);
  }

  // Object operations (JSON)
  static Future<void> setObject(String key, Map<String, dynamic> value) async {
    await _instance.setString(key, jsonEncode(value));
  }

  static Map<String, dynamic>? getObject(String key) {
    final value = _instance.getString(key);
    if (value != null) {
      try {
        return jsonDecode(value) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // List operations (JSON)
  static Future<void> setList(String key, List<dynamic> value) async {
    await _instance.setString(key, jsonEncode(value));
  }

  static List<dynamic>? getList(String key) {
    final value = _instance.getString(key);
    if (value != null) {
      try {
        return jsonDecode(value) as List<dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Remove operations
  static Future<void> remove(String key) async {
    await _instance.remove(key);
  }

  static Future<void> clear() async {
    await _instance.clear();
  }

  // Check operations
  static bool containsKey(String key) {
    return _instance.containsKey(key);
  }

  static Set<String> getAllKeys() {
    return _instance.getKeys();
  }

  // Secure storage methods (using regular storage for simplicity)
  static Future<void> setSecureString(String key, String value) async {
    await setString('secure_$key', value);
  }

  static String? getSecureString(String key) {
    return getString('secure_$key');
  }

  static Future<void> setSecureObject(String key, Map<String, dynamic> value) async {
    await setObject('secure_$key', value);
  }

  static Map<String, dynamic>? getSecureObject(String key) {
    return getObject('secure_$key');
  }

  static Future<void> removeSecure(String key) async {
    await remove('secure_$key');
  }

  static Future<void> clearSecure() async {
    final keys = getAllKeys();
    for (final key in keys) {
      if (key.startsWith('secure_')) {
        await remove(key);
      }
    }
  }

  static bool containsSecureKey(String key) {
    return containsKey('secure_$key');
  }

  // Cache with expiry
  static Future<void> setCacheWithExpiry(
    String key,
    dynamic value,
    Duration expiry,
  ) async {
    final cacheData = {
      'value': value,
      'expiry': DateTime.now().add(expiry).millisecondsSinceEpoch,
    };
    await setObject('cache_$key', cacheData);
  }

  static T? getCacheWithExpiry<T>(String key) {
    final cacheData = getObject('cache_$key');
    if (cacheData != null) {
      final expiry = cacheData['expiry'] as int?;
      if (expiry != null && DateTime.now().millisecondsSinceEpoch < expiry) {
        return cacheData['value'] as T?;
      } else {
        // Cache expired, remove it
        remove('cache_$key');
      }
    }
    return null;
  }

  static Future<void> clearExpiredCache() async {
    final keys = getAllKeys();
    final now = DateTime.now().millisecondsSinceEpoch;
    
    for (final key in keys) {
      if (key.startsWith('cache_')) {
        final cacheData = getObject(key);
        if (cacheData != null) {
          final expiry = cacheData['expiry'] as int?;
          if (expiry != null && now >= expiry) {
            await remove(key);
          }
        }
      }
    }
  }

  // User preferences
  static Future<void> setUserPreference(String key, dynamic value) async {
    await setObject('pref_$key', {'value': value});
  }

  static T? getUserPreference<T>(String key) {
    final data = getObject('pref_$key');
    return data?['value'] as T?;
  }

  static Future<void> removeUserPreference(String key) async {
    await remove('pref_$key');
  }

  static Future<void> clearUserPreferences() async {
    final keys = getAllKeys();
    for (final key in keys) {
      if (key.startsWith('pref_')) {
        await remove(key);
      }
    }
  }

  // App-specific methods
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    await setSecureObject('user_data', userData);
  }

  static Map<String, dynamic>? getUserData() {
    return getSecureObject('user_data');
  }

  static Future<void> clearUserData() async {
    await removeSecure('user_data');
  }

  static Future<void> saveAuthToken(String token) async {
    await setSecureString('auth_token', token);
  }

  static String? getAuthToken() {
    return getSecureString('auth_token');
  }

  static Future<void> clearAuthToken() async {
    await removeSecure('auth_token');
  }

  // App settings
  static Future<void> setThemeMode(String mode) async {
    await setUserPreference('theme_mode', mode);
  }

  static String getThemeMode() {
    return getUserPreference<String>('theme_mode') ?? 'system';
  }

  static Future<void> setLanguage(String language) async {
    await setUserPreference('language', language);
  }

  static String getLanguage() {
    return getUserPreference<String>('language') ?? 'ar';
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await setUserPreference('notifications_enabled', enabled);
  }

  static bool getNotificationsEnabled() {
    return getUserPreference<bool>('notifications_enabled') ?? true;
  }

  // First time app launch
  static Future<void> setFirstLaunch(bool isFirst) async {
    await setBool('is_first_launch', isFirst);
  }

  static bool isFirstLaunch() {
    return getBool('is_first_launch') ?? true;
  }

  // Onboarding completion
  static Future<void> setOnboardingCompleted(bool completed) async {
    await setBool('onboarding_completed', completed);
  }

  static bool isOnboardingCompleted() {
    return getBool('onboarding_completed') ?? false;
  }
}
