import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String name;
  final String email;
  @<PERSON><PERSON><PERSON>ey(name: 'email_verified_at')
  final String? emailVerifiedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String updatedAt;
  final String? username;
  final String? phone;
  final String? avatar;
  final bool? isActive;
  final List<String>? roles;
  final List<String>? permissions;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
    this.username,
    this.phone,
    this.avatar,
    this.isActive,
    this.roles,
    this.permissions,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? name,
    String? email,
    String? emailVerifiedAt,
    String? createdAt,
    String? updatedAt,
    String? username,
    String? phone,
    String? avatar,
    bool? isActive,
    List<String>? roles,
    List<String>? permissions,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      roles: roles ?? this.roles,
      permissions: permissions ?? this.permissions,
    );
  }

  bool hasRole(String role) {
    return roles?.contains(role) ?? false;
  }

  bool hasPermission(String permission) {
    return permissions?.contains(permission) ?? false;
  }

  bool get isAdmin => hasRole('admin');
  bool get isManager => hasRole('manager');
  bool get isUser => hasRole('user');
}

@JsonSerializable()
class AuthResponse {
  final User user;
  final String token;
  @JsonKey(name: 'token_type')
  final String tokenType;
  @JsonKey(name: 'expires_in')
  final int? expiresIn;

  AuthResponse({
    required this.user,
    required this.token,
    required this.tokenType,
    this.expiresIn,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) => _$AuthResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;
  @JsonKey(name: 'remember_me')
  final bool? rememberMe;

  LoginRequest({
    required this.email,
    required this.password,
    this.rememberMe,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class RegisterRequest {
  final String name;
  final String email;
  final String password;
  @JsonKey(name: 'password_confirmation')
  final String passwordConfirmation;
  final String? username;
  final String? phone;

  RegisterRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.passwordConfirmation,
    this.username,
    this.phone,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

@JsonSerializable()
class UpdateProfileRequest {
  final String? name;
  final String? email;
  final String? username;
  final String? phone;
  @JsonKey(name: 'current_password')
  final String? currentPassword;
  @JsonKey(name: 'new_password')
  final String? newPassword;
  @JsonKey(name: 'new_password_confirmation')
  final String? newPasswordConfirmation;

  UpdateProfileRequest({
    this.name,
    this.email,
    this.username,
    this.phone,
    this.currentPassword,
    this.newPassword,
    this.newPasswordConfirmation,
  });

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) => _$UpdateProfileRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateProfileRequestToJson(this);
}

@JsonSerializable()
class ForgotPasswordRequest {
  final String email;

  ForgotPasswordRequest({required this.email});

  factory ForgotPasswordRequest.fromJson(Map<String, dynamic> json) => _$ForgotPasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ForgotPasswordRequestToJson(this);
}

@JsonSerializable()
class ResetPasswordRequest {
  final String email;
  final String token;
  final String password;
  @JsonKey(name: 'password_confirmation')
  final String passwordConfirmation;

  ResetPasswordRequest({
    required this.email,
    required this.token,
    required this.password,
    required this.passwordConfirmation,
  });

  factory ResetPasswordRequest.fromJson(Map<String, dynamic> json) => _$ResetPasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ResetPasswordRequestToJson(this);
}
