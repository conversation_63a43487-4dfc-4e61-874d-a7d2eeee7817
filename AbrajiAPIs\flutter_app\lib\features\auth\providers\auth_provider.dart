import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';
import '../models/user_model.dart';

class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final User? user;
  final String? error;

  AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    User? user,
    String? error,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      error: error,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final token = await ApiService.getAuthToken();
      if (token != null) {
        final userData = await StorageService.getObject(AppConfig.userKey);
        if (userData != null) {
          final user = User.fromJson(userData);
          state = state.copyWith(
            isLoading: false,
            isAuthenticated: true,
            user: user,
          );
          return;
        }
      }
      
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        error: 'خطأ في التحقق من حالة المصادقة',
      );
    }
  }

  Future<bool> login({
    required String email,
    required String password,
    bool? rememberMe,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final loginRequest = LoginRequest(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      final response = await ApiService.post<AuthResponse>(
        AppConfig.authEndpoint,
        data: loginRequest.toJson(),
        fromJson: (json) => AuthResponse.fromJson(json['data'] ?? json),
      );

      if (response.isSuccess && response.data != null) {
        final authResponse = response.data!;
        
        // Save token and user data
        await ApiService.setAuthToken(authResponse.token);
        await StorageService.setObject(AppConfig.userKey, authResponse.user.toJson());
        
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: authResponse.user,
        );
        
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'فشل في تسجيل الدخول',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في الاتصال بالخادم',
      );
      return false;
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? username,
    String? phone,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final registerRequest = RegisterRequest(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        username: username,
        phone: phone,
      );

      final response = await ApiService.post<AuthResponse>(
        '/auth/register',
        data: registerRequest.toJson(),
        fromJson: (json) => AuthResponse.fromJson(json['data'] ?? json),
      );

      if (response.isSuccess && response.data != null) {
        final authResponse = response.data!;
        
        // Save token and user data
        await ApiService.setAuthToken(authResponse.token);
        await StorageService.setObject(AppConfig.userKey, authResponse.user.toJson());
        
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: authResponse.user,
        );
        
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'فشل في إنشاء الحساب',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في الاتصال بالخادم',
      );
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      // Call logout API
      await ApiService.post('/auth/logout');
    } catch (e) {
      // Continue with logout even if API call fails
    }

    // Clear local data
    await ApiService.clearAuthToken();
    await StorageService.remove(AppConfig.userKey);
    
    state = AuthState();
  }

  Future<bool> updateProfile({
    String? name,
    String? email,
    String? username,
    String? phone,
    String? currentPassword,
    String? newPassword,
    String? newPasswordConfirmation,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updateRequest = UpdateProfileRequest(
        name: name,
        email: email,
        username: username,
        phone: phone,
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
      );

      final response = await ApiService.put<User>(
        '/auth/profile',
        data: updateRequest.toJson(),
        fromJson: (json) => User.fromJson(json['data'] ?? json),
      );

      if (response.isSuccess && response.data != null) {
        final updatedUser = response.data!;
        
        // Update stored user data
        await StorageService.setObject(AppConfig.userKey, updatedUser.toJson());
        
        state = state.copyWith(
          isLoading: false,
          user: updatedUser,
        );
        
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'فشل في تحديث الملف الشخصي',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في الاتصال بالخادم',
      );
      return false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = ForgotPasswordRequest(email: email);

      final response = await ApiService.post(
        '/auth/forgot-password',
        data: request.toJson(),
      );

      if (response.isSuccess) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'فشل في إرسال رابط إعادة تعيين كلمة المرور',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في الاتصال بالخادم',
      );
      return false;
    }
  }

  Future<bool> resetPassword({
    required String email,
    required String token,
    required String password,
    required String passwordConfirmation,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = ResetPasswordRequest(
        email: email,
        token: token,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      final response = await ApiService.post(
        '/auth/reset-password',
        data: request.toJson(),
      );

      if (response.isSuccess) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'فشل في إعادة تعيين كلمة المرور',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في الاتصال بالخادم',
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});
