import '../models/user_model.dart';

class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final User? user;
  final String? error;

  AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    User? user,
    String? error,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      error: error,
    );
  }
}

class AuthNotifier {
  AuthState _state = AuthState();
  
  AuthState get state => _state;
  
  void _setState(AuthState newState) {
    _state = newState;
  }

  AuthNotifier() {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    _setState(_state.copyWith(isLoading: true));
    
    try {
      // Simulate checking auth status
      await Future.delayed(const Duration(milliseconds: 500));
      
      _setState(_state.copyWith(
        isLoading: false,
        isAuthenticated: false,
      ));
    } catch (e) {
      _setState(_state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        error: 'خطأ في التحقق من حالة المصادقة',
      ));
    }
  }

  Future<bool> login({
    required String email,
    required String password,
    bool? rememberMe,
  }) async {
    _setState(_state.copyWith(isLoading: true, error: null));

    try {
      // Simulate login API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock successful login
      final user = User(
        id: 1,
        name: 'مستخدم تجريبي',
        email: email,
        username: 'test_user',
        phone: '+966501234567',
        isEmailVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _setState(_state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        user: user,
      ));
      
      return true;
    } catch (e) {
      _setState(_state.copyWith(
        isLoading: false,
        error: 'خطأ في تسجيل الدخول',
      ));
      return false;
    }
  }

  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? username,
    String? phone,
  }) async {
    _setState(_state.copyWith(isLoading: true, error: null));

    try {
      // Simulate register API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock successful registration
      final user = User(
        id: 1,
        name: name,
        email: email,
        username: username ?? 'new_user',
        phone: phone,
        isEmailVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _setState(_state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        user: user,
      ));
      
      return true;
    } catch (e) {
      _setState(_state.copyWith(
        isLoading: false,
        error: 'خطأ في إنشاء الحساب',
      ));
      return false;
    }
  }

  Future<void> logout() async {
    _setState(_state.copyWith(isLoading: true));

    try {
      // Simulate logout API call
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      // Handle logout error if needed
    }
    
    // Clear auth state
    _setState(AuthState());
  }

  Future<bool> updateProfile({
    String? name,
    String? email,
    String? username,
    String? phone,
    String? currentPassword,
    String? newPassword,
    String? newPasswordConfirmation,
  }) async {
    _setState(_state.copyWith(isLoading: true, error: null));

    try {
      // Simulate update profile API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock successful update
      final updatedUser = _state.user?.copyWith(
        name: name ?? _state.user?.name,
        email: email ?? _state.user?.email,
        username: username ?? _state.user?.username,
        phone: phone ?? _state.user?.phone,
        updatedAt: DateTime.now(),
      );
      
      _setState(_state.copyWith(
        isLoading: false,
        user: updatedUser,
      ));
      
      return true;
    } catch (e) {
      _setState(_state.copyWith(
        isLoading: false,
        error: 'خطأ في تحديث الملف الشخصي',
      ));
      return false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    _setState(_state.copyWith(isLoading: true, error: null));

    try {
      // Simulate forgot password API call
      await Future.delayed(const Duration(seconds: 1));
      
      _setState(_state.copyWith(isLoading: false));
      return true;
    } catch (e) {
      _setState(_state.copyWith(
        isLoading: false,
        error: 'خطأ في إرسال رابط إعادة تعيين كلمة المرور',
      ));
      return false;
    }
  }

  Future<bool> resetPassword({
    required String token,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    _setState(_state.copyWith(isLoading: true, error: null));

    try {
      // Simulate reset password API call
      await Future.delayed(const Duration(seconds: 1));
      
      _setState(_state.copyWith(isLoading: false));
      return true;
    } catch (e) {
      _setState(_state.copyWith(
        isLoading: false,
        error: 'خطأ في إعادة تعيين كلمة المرور',
      ));
      return false;
    }
  }

  void clearError() {
    _setState(_state.copyWith(error: null));
  }
}

// Global instance
final authProvider = AuthNotifier();
