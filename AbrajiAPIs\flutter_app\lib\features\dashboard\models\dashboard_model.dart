class DashboardData {
  final int totalInvoices;
  final double totalSales;
  final double pendingPayments;
  final int activeCustomers;
  final double invoicesTrend;
  final double salesTrend;
  final double pendingTrend;
  final double customersTrend;
  final List<double> salesChartData;
  final List<RecentInvoice> recentInvoices;
  final List<RecentTransaction> recentTransactions;

  DashboardData({
    required this.totalInvoices,
    required this.totalSales,
    required this.pendingPayments,
    required this.activeCustomers,
    required this.invoicesTrend,
    required this.salesTrend,
    required this.pendingTrend,
    required this.customersTrend,
    required this.salesChartData,
    required this.recentInvoices,
    required this.recentTransactions,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      totalInvoices: json['total_invoices'] ?? 0,
      totalSales: (json['total_sales'] ?? 0).toDouble(),
      pendingPayments: (json['pending_payments'] ?? 0).toDouble(),
      activeCustomers: json['active_customers'] ?? 0,
      invoicesTrend: (json['invoices_trend'] ?? 0).toDouble(),
      salesTrend: (json['sales_trend'] ?? 0).toDouble(),
      pendingTrend: (json['pending_trend'] ?? 0).toDouble(),
      customersTrend: (json['customers_trend'] ?? 0).toDouble(),
      salesChartData: List<double>.from(
        (json['sales_chart_data'] ?? []).map((x) => x.toDouble()),
      ),
      recentInvoices: List<RecentInvoice>.from(
        (json['recent_invoices'] ?? []).map((x) => RecentInvoice.fromJson(x)),
      ),
      recentTransactions: List<RecentTransaction>.from(
        (json['recent_transactions'] ?? []).map((x) => RecentTransaction.fromJson(x)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_invoices': totalInvoices,
      'total_sales': totalSales,
      'pending_payments': pendingPayments,
      'active_customers': activeCustomers,
      'invoices_trend': invoicesTrend,
      'sales_trend': salesTrend,
      'pending_trend': pendingTrend,
      'customers_trend': customersTrend,
      'sales_chart_data': salesChartData,
      'recent_invoices': recentInvoices.map((x) => x.toJson()).toList(),
      'recent_transactions': recentTransactions.map((x) => x.toJson()).toList(),
    };
  }
}

class RecentInvoice {
  final int id;
  final String invoiceNumber;
  final String customerName;
  final double amount;
  final String status;
  final String createdAt;

  RecentInvoice({
    required this.id,
    required this.invoiceNumber,
    required this.customerName,
    required this.amount,
    required this.status,
    required this.createdAt,
  });

  factory RecentInvoice.fromJson(Map<String, dynamic> json) {
    return RecentInvoice(
      id: json['id'],
      invoiceNumber: json['invoice_number'] ?? '',
      customerName: json['customer_name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      status: json['status'] ?? '',
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'customer_name': customerName,
      'amount': amount,
      'status': status,
      'created_at': createdAt,
    };
  }
}

class RecentTransaction {
  final int id;
  final String type;
  final String description;
  final double amount;
  final String status;
  final String createdAt;

  RecentTransaction({
    required this.id,
    required this.type,
    required this.description,
    required this.amount,
    required this.status,
    required this.createdAt,
  });

  factory RecentTransaction.fromJson(Map<String, dynamic> json) {
    return RecentTransaction(
      id: json['id'],
      type: json['type'] ?? '',
      description: json['description'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      status: json['status'] ?? '',
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'amount': amount,
      'status': status,
      'created_at': createdAt,
    };
  }
}
