import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/services/api_service.dart';
import '../models/dashboard_model.dart';

class DashboardState {
  final bool isLoading;
  final DashboardData? data;
  final String? error;

  DashboardState({
    this.isLoading = false,
    this.data,
    this.error,
  });

  DashboardState copyWith({
    bool? isLoading,
    DashboardData? data,
    String? error,
  }) {
    return DashboardState(
      isLoading: isLoading ?? this.isLoading,
      data: data ?? this.data,
      error: error,
    );
  }
}

class DashboardNotifier extends StateNotifier<DashboardState> {
  DashboardNotifier() : super(DashboardState());

  Future<void> loadDashboardData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await ApiService.get<DashboardData>(
        '/dashboard',
        fromJson: (json) => DashboardData.fromJson(json['data'] ?? json),
      );

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          data: response.data,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'فشل في تحميل بيانات لوحة التحكم',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'خطأ في الاتصال بالخادم',
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final dashboardProvider = StateNotifierProvider<DashboardNotifier, DashboardState>((ref) {
  return DashboardNotifier();
});
