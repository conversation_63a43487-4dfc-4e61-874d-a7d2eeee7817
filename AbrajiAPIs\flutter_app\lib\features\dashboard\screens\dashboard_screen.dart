import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../providers/dashboard_provider.dart';
import '../widgets/stats_card.dart';
import '../widgets/recent_invoices_widget.dart';
import '../widgets/recent_transactions_widget.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(dashboardProvider.notifier).loadDashboardData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.bell),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.userCircle),
            onPressed: () {
              // Navigate to profile
            },
          ),
        ],
      ),
      body: dashboardState.isLoading
          ? const LoadingWidget()
          : RefreshIndicator(
              onRefresh: () async {
                await ref.read(dashboardProvider.notifier).loadDashboardData();
              },
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConfig.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Message
                    CustomCard(
                      child: Padding(
                        padding: const EdgeInsets.all(AppConfig.defaultPadding),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(AppConfig.defaultRadius),
                              ),
                              child: const Icon(
                                FontAwesomeIcons.chartLine,
                                color: AppTheme.primaryColor,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'مرحباً بك',
                                    style: Theme.of(context).textTheme.titleLarge,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'إليك ملخص أعمالك اليوم',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Statistics Cards
                    if (dashboardState.data != null) ...[
                      GridView.count(
                        crossAxisCount: 2,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisSpacing: AppConfig.defaultPadding,
                        mainAxisSpacing: AppConfig.defaultPadding,
                        childAspectRatio: 1.5,
                        children: [
                          StatsCard(
                            title: 'إجمالي الفواتير',
                            value: dashboardState.data!.totalInvoices.toString(),
                            icon: FontAwesomeIcons.fileInvoice,
                            color: AppTheme.primaryColor,
                            trend: dashboardState.data!.invoicesTrend,
                          ),
                          StatsCard(
                            title: 'إجمالي المبيعات',
                            value: '${dashboardState.data!.totalSales.toStringAsFixed(2)} ر.س',
                            icon: FontAwesomeIcons.dollarSign,
                            color: AppTheme.successColor,
                            trend: dashboardState.data!.salesTrend,
                          ),
                          StatsCard(
                            title: 'المدفوعات المعلقة',
                            value: '${dashboardState.data!.pendingPayments.toStringAsFixed(2)} ر.س',
                            icon: FontAwesomeIcons.clock,
                            color: AppTheme.warningColor,
                            trend: dashboardState.data!.pendingTrend,
                          ),
                          StatsCard(
                            title: 'العملاء النشطين',
                            value: dashboardState.data!.activeCustomers.toString(),
                            icon: FontAwesomeIcons.users,
                            color: AppTheme.secondaryColor,
                            trend: dashboardState.data!.customersTrend,
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 30),
                      
                      // Charts Section
                      Text(
                        'الإحصائيات',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      CustomCard(
                        child: Padding(
                          padding: const EdgeInsets.all(AppConfig.defaultPadding),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'المبيعات الشهرية',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 20),
                              SizedBox(
                                height: 200,
                                child: LineChart(
                                  LineChartData(
                                    gridData: FlGridData(show: false),
                                    titlesData: FlTitlesData(show: false),
                                    borderData: FlBorderData(show: false),
                                    lineBarsData: [
                                      LineChartBarData(
                                        spots: dashboardState.data!.salesChartData
                                            .asMap()
                                            .entries
                                            .map((e) => FlSpot(e.key.toDouble(), e.value))
                                            .toList(),
                                        isCurved: true,
                                        color: AppTheme.primaryColor,
                                        barWidth: 3,
                                        dotData: FlDotData(show: false),
                                        belowBarData: BarAreaData(
                                          show: true,
                                          color: AppTheme.primaryColor.withOpacity(0.1),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 30),
                      
                      // Recent Activities
                      Text(
                        'النشاطات الأخيرة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Recent Invoices
                      const RecentInvoicesWidget(),
                      
                      const SizedBox(height: 20),
                      
                      // Recent Transactions
                      const RecentTransactionsWidget(),
                    ],
                    
                    // Error State
                    if (dashboardState.error != null)
                      CustomCard(
                        child: Padding(
                          padding: const EdgeInsets.all(AppConfig.defaultPadding),
                          child: Column(
                            children: [
                              const Icon(
                                FontAwesomeIcons.exclamationTriangle,
                                color: AppTheme.errorColor,
                                size: 48,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'حدث خطأ في تحميل البيانات',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                dashboardState.error!,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: () {
                                  ref.read(dashboardProvider.notifier).loadDashboardData();
                                },
                                icon: const Icon(FontAwesomeIcons.redo),
                                label: const Text('إعادة المحاولة'),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
    );
  }
}
