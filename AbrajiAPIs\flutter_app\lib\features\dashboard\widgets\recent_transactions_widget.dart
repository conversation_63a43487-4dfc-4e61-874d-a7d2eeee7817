import 'package:flutter/material.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_card.dart';

class RecentTransactionsWidget extends StatelessWidget {
  const RecentTransactionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المعاملات الأخيرة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all transactions
                },
                child: const Text('عرض الكل'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3, // Mock data
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              return _buildTransactionItem(context, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(BuildContext context, dynamic transaction) {
    final type = transaction.type?.toLowerCase() ?? '';
    final isIncome = type == 'income' || type == 'دخل';
    final isExpense = type == 'expense' || type == 'مصروف';

    IconData icon;
    Color color;
    String prefix;

    if (isIncome) {
      icon = Icons.arrow_upward;
      color = AppTheme.successColor;
      prefix = '+';
    } else if (isExpense) {
      icon = Icons.arrow_downward;
      color = AppTheme.errorColor;
      prefix = '-';
    } else {
      icon = Icons.swap_horiz;
      color = AppTheme.primaryColor;
      prefix = '';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppConfig.smallRadius),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description ?? 'معاملة #${transaction.id}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getTypeText(type),
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  transaction.createdAt?.split('T')[0] ?? '',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '$prefix${transaction.amount?.toStringAsFixed(2) ?? '0.00'} ر.س',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              _buildStatusBadge(transaction.status ?? 'completed'),
            ],
          ),
        ],
      ),
    );
  }

  String _getTypeText(String type) {
    switch (type.toLowerCase()) {
      case 'income':
      case 'دخل':
        return 'دخل';
      case 'expense':
      case 'مصروف':
        return 'مصروف';
      case 'transfer':
      case 'تحويل':
        return 'تحويل';
      default:
        return 'معاملة';
    }
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    String text;

    switch (status.toLowerCase()) {
      case 'completed':
      case 'مكتملة':
        color = AppTheme.successColor;
        text = 'مكتملة';
        break;
      case 'pending':
      case 'معلقة':
        color = AppTheme.warningColor;
        text = 'معلقة';
        break;
      case 'failed':
      case 'فاشلة':
        color = AppTheme.errorColor;
        text = 'فاشلة';
        break;
      case 'cancelled':
      case 'ملغية':
        color = Colors.grey;
        text = 'ملغية';
        break;
      default:
        color = Colors.grey;
        text = 'غير محدد';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
