import 'package:json_annotation/json_annotation.dart';

part 'invoice_model.g.dart';

@JsonSerializable()
class Invoice {
  final int id;
  @<PERSON>son<PERSON><PERSON>(name: 'invoice_number')
  final String invoiceNumber;
  @Json<PERSON>ey(name: 'customer_name')
  final String customerName;
  @Json<PERSON>ey(name: 'customer_email')
  final String? customerEmail;
  @<PERSON>sonKey(name: 'customer_phone')
  final String? customerPhone;
  @JsonKey(name: 'customer_address')
  final String? customerAddress;
  @JsonKey(name: 'issue_date')
  final String issueDate;
  @<PERSON>sonKey(name: 'due_date')
  final String? dueDate;
  final String status;
  @<PERSON>sonKey(name: 'subtotal')
  final double subtotal;
  @<PERSON>son<PERSON><PERSON>(name: 'tax_amount')
  final double taxAmount;
  @JsonKey(name: 'discount_amount')
  final double discountAmount;
  @<PERSON>son<PERSON>ey(name: 'total_amount')
  final double totalAmount;
  @<PERSON>sonKey(name: 'paid_amount')
  final double paidAmount;
  @<PERSON>son<PERSON>ey(name: 'remaining_amount')
  final double remainingAmount;
  final String? notes;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  @<PERSON>son<PERSON>ey(name: 'updated_at')
  final String updatedAt;
  @<PERSON>son<PERSON><PERSON>(name: 'created_by')
  final int? createdBy;
  final List<InvoiceItem>? items;
  final List<InvoicePayment>? payments;

  Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    required this.issueDate,
    this.dueDate,
    required this.status,
    required this.subtotal,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.paidAmount,
    required this.remainingAmount,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.items,
    this.payments,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) => _$InvoiceFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceToJson(this);

  bool get isPaid => status == 'paid';
  bool get isPending => status == 'pending';
  bool get isOverdue => status == 'overdue';
  bool get isCancelled => status == 'cancelled';
  bool get isDraft => status == 'draft';

  double get paidPercentage => totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < totalAmount;
  bool get isFullyPaid => paidAmount >= totalAmount;
}

@JsonSerializable()
class InvoiceItem {
  final int id;
  @JsonKey(name: 'invoice_id')
  final int invoiceId;
  final String description;
  final int quantity;
  @JsonKey(name: 'unit_price')
  final double unitPrice;
  @JsonKey(name: 'total_price')
  final double totalPrice;
  @JsonKey(name: 'created_at')
  final String createdAt;
  @JsonKey(name: 'updated_at')
  final String updatedAt;

  InvoiceItem({
    required this.id,
    required this.invoiceId,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    required this.createdAt,
    required this.updatedAt,
  });

  factory InvoiceItem.fromJson(Map<String, dynamic> json) => _$InvoiceItemFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceItemToJson(this);
}

@JsonSerializable()
class InvoicePayment {
  final int id;
  @JsonKey(name: 'invoice_id')
  final int invoiceId;
  final double amount;
  @JsonKey(name: 'payment_method')
  final String paymentMethod;
  @JsonKey(name: 'payment_date')
  final String paymentDate;
  final String? reference;
  final String? notes;
  @JsonKey(name: 'created_at')
  final String createdAt;
  @JsonKey(name: 'updated_at')
  final String updatedAt;

  InvoicePayment({
    required this.id,
    required this.invoiceId,
    required this.amount,
    required this.paymentMethod,
    required this.paymentDate,
    this.reference,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory InvoicePayment.fromJson(Map<String, dynamic> json) => _$InvoicePaymentFromJson(json);
  Map<String, dynamic> toJson() => _$InvoicePaymentToJson(this);
}

@JsonSerializable()
class CreateInvoiceRequest {
  @JsonKey(name: 'customer_name')
  final String customerName;
  @JsonKey(name: 'customer_email')
  final String? customerEmail;
  @JsonKey(name: 'customer_phone')
  final String? customerPhone;
  @JsonKey(name: 'customer_address')
  final String? customerAddress;
  @JsonKey(name: 'issue_date')
  final String issueDate;
  @JsonKey(name: 'due_date')
  final String? dueDate;
  @JsonKey(name: 'tax_amount')
  final double? taxAmount;
  @JsonKey(name: 'discount_amount')
  final double? discountAmount;
  final String? notes;
  final List<CreateInvoiceItemRequest> items;

  CreateInvoiceRequest({
    required this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    required this.issueDate,
    this.dueDate,
    this.taxAmount,
    this.discountAmount,
    this.notes,
    required this.items,
  });

  factory CreateInvoiceRequest.fromJson(Map<String, dynamic> json) => _$CreateInvoiceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateInvoiceRequestToJson(this);
}

@JsonSerializable()
class CreateInvoiceItemRequest {
  final String description;
  final int quantity;
  @JsonKey(name: 'unit_price')
  final double unitPrice;

  CreateInvoiceItemRequest({
    required this.description,
    required this.quantity,
    required this.unitPrice,
  });

  factory CreateInvoiceItemRequest.fromJson(Map<String, dynamic> json) => _$CreateInvoiceItemRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateInvoiceItemRequestToJson(this);

  double get totalPrice => quantity * unitPrice;
}

@JsonSerializable()
class UpdateInvoiceRequest {
  @JsonKey(name: 'customer_name')
  final String? customerName;
  @JsonKey(name: 'customer_email')
  final String? customerEmail;
  @JsonKey(name: 'customer_phone')
  final String? customerPhone;
  @JsonKey(name: 'customer_address')
  final String? customerAddress;
  @JsonKey(name: 'issue_date')
  final String? issueDate;
  @JsonKey(name: 'due_date')
  final String? dueDate;
  final String? status;
  @JsonKey(name: 'tax_amount')
  final double? taxAmount;
  @JsonKey(name: 'discount_amount')
  final double? discountAmount;
  final String? notes;

  UpdateInvoiceRequest({
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    this.issueDate,
    this.dueDate,
    this.status,
    this.taxAmount,
    this.discountAmount,
    this.notes,
  });

  factory UpdateInvoiceRequest.fromJson(Map<String, dynamic> json) => _$UpdateInvoiceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateInvoiceRequestToJson(this);
}

@JsonSerializable()
class AddPaymentRequest {
  final double amount;
  @JsonKey(name: 'payment_method')
  final String paymentMethod;
  @JsonKey(name: 'payment_date')
  final String paymentDate;
  final String? reference;
  final String? notes;

  AddPaymentRequest({
    required this.amount,
    required this.paymentMethod,
    required this.paymentDate,
    this.reference,
    this.notes,
  });

  factory AddPaymentRequest.fromJson(Map<String, dynamic> json) => _$AddPaymentRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AddPaymentRequestToJson(this);
}

@JsonSerializable()
class InvoiceListResponse {
  final List<Invoice> data;
  final PaginationMeta meta;

  InvoiceListResponse({
    required this.data,
    required this.meta,
  });

  factory InvoiceListResponse.fromJson(Map<String, dynamic> json) => _$InvoiceListResponseFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceListResponseToJson(this);
}

@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;
  @JsonKey(name: 'last_page')
  final int lastPage;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'has_more_pages')
  final bool hasMorePages;

  PaginationMeta({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    required this.hasMorePages,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) => _$PaginationMetaFromJson(json);
  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}
