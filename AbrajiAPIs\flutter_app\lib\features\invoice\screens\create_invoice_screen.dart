import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/custom_button.dart';

class CreateInvoiceScreen extends ConsumerStatefulWidget {
  const CreateInvoiceScreen({super.key});

  @override
  ConsumerState<CreateInvoiceScreen> createState() => _CreateInvoiceScreenState();
}

class _CreateInvoiceScreenState extends ConsumerState<CreateInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _customerEmailController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerAddressController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime _issueDate = DateTime.now();
  DateTime? _dueDate;
  double _taxRate = 15.0;
  double _discountAmount = 0.0;
  
  List<InvoiceItemData> _items = [InvoiceItemData()];

  @override
  void dispose() {
    _customerNameController.dispose();
    _customerEmailController.dispose();
    _customerPhoneController.dispose();
    _customerAddressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء فاتورة جديدة'),
        actions: [
          TextButton(
            onPressed: _saveAsDraft,
            child: const Text('حفظ كمسودة'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Customer Information
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات العميل',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    CustomTextField(
                      controller: _customerNameController,
                      label: 'اسم العميل *',
                      hint: 'أدخل اسم العميل',
                      prefixIcon: FontAwesomeIcons.user,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'اسم العميل مطلوب';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    EmailTextField(
                      controller: _customerEmailController,
                      label: 'البريد الإلكتروني',
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    PhoneTextField(
                      controller: _customerPhoneController,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    CustomTextField(
                      controller: _customerAddressController,
                      label: 'العنوان',
                      hint: 'أدخل عنوان العميل',
                      prefixIcon: FontAwesomeIcons.mapMarkerAlt,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Invoice Details
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الفاتورة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: _buildDateField(
                            'تاريخ الإصدار *',
                            _issueDate,
                            (date) => setState(() => _issueDate = date),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildDateField(
                            'تاريخ الاستحقاق',
                            _dueDate,
                            (date) => setState(() => _dueDate = date),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            label: 'معدل الضريبة (%)',
                            hint: '15',
                            prefixIcon: FontAwesomeIcons.percentage,
                            keyboardType: TextInputType.number,
                            initialValue: _taxRate.toString(),
                            onChanged: (value) {
                              _taxRate = double.tryParse(value) ?? 0.0;
                              setState(() {});
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: CustomTextField(
                            label: 'مبلغ الخصم',
                            hint: '0',
                            prefixIcon: FontAwesomeIcons.minus,
                            keyboardType: TextInputType.number,
                            initialValue: _discountAmount.toString(),
                            onChanged: (value) {
                              _discountAmount = double.tryParse(value) ?? 0.0;
                              setState(() {});
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Invoice Items
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'عناصر الفاتورة',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: _addItem,
                          icon: const Icon(FontAwesomeIcons.plus),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _items.length,
                      separatorBuilder: (context, index) => const SizedBox(height: 16),
                      itemBuilder: (context, index) {
                        return _buildInvoiceItem(index);
                      },
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Invoice Summary
              CustomCard(
                child: Column(
                  children: [
                    _buildSummaryRow('المجموع الفرعي', _calculateSubtotal()),
                    _buildSummaryRow('الضريبة (${_taxRate.toStringAsFixed(1)}%)', _calculateTax()),
                    _buildSummaryRow('الخصم', _discountAmount),
                    const Divider(thickness: 2),
                    _buildSummaryRow('المجموع الكلي', _calculateTotal(), isTotal: true),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Notes
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ملاحظات',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    CustomTextField(
                      controller: _notesController,
                      hint: 'أضف ملاحظات إضافية...',
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: SecondaryButton(
                      text: 'حفظ كمسودة',
                      onPressed: _saveAsDraft,
                      icon: FontAwesomeIcons.save,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomButton(
                      text: 'إنشاء الفاتورة',
                      onPressed: _createInvoice,
                      icon: FontAwesomeIcons.check,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateField(String label, DateTime? date, Function(DateTime) onChanged) {
    return CustomTextField(
      label: label,
      hint: 'اختر التاريخ',
      prefixIcon: FontAwesomeIcons.calendar,
      readOnly: true,
      initialValue: date?.toString().split(' ')[0] ?? '',
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (selectedDate != null) {
          onChanged(selectedDate);
        }
      },
    );
  }

  Widget _buildInvoiceItem(int index) {
    final item = _items[index];
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(AppConfig.defaultRadius),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'عنصر ${index + 1}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (_items.length > 1)
                IconButton(
                  onPressed: () => _removeItem(index),
                  icon: const Icon(FontAwesomeIcons.trash, size: 16, color: Colors.red),
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          CustomTextField(
            label: 'الوصف *',
            hint: 'وصف الخدمة أو المنتج',
            initialValue: item.description,
            onChanged: (value) => item.description = value,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'الوصف مطلوب';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  label: 'الكمية *',
                  hint: '1',
                  keyboardType: TextInputType.number,
                  initialValue: item.quantity.toString(),
                  onChanged: (value) {
                    item.quantity = int.tryParse(value) ?? 1;
                    setState(() {});
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الكمية مطلوبة';
                    }
                    if (int.tryParse(value) == null || int.parse(value) <= 0) {
                      return 'الكمية غير صحيحة';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomTextField(
                  label: 'السعر *',
                  hint: '0.00',
                  keyboardType: TextInputType.number,
                  initialValue: item.unitPrice.toString(),
                  onChanged: (value) {
                    item.unitPrice = double.tryParse(value) ?? 0.0;
                    setState(() {});
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'السعر مطلوب';
                    }
                    if (double.tryParse(value) == null || double.parse(value) < 0) {
                      return 'السعر غير صحيح';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomTextField(
                  label: 'المجموع',
                  initialValue: (item.quantity * item.unitPrice).toStringAsFixed(2),
                  readOnly: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            '${value.toStringAsFixed(2)} ر.س',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? AppTheme.primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  void _addItem() {
    setState(() {
      _items.add(InvoiceItemData());
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  double _calculateSubtotal() {
    return _items.fold(0.0, (sum, item) => sum + (item.quantity * item.unitPrice));
  }

  double _calculateTax() {
    return _calculateSubtotal() * (_taxRate / 100);
  }

  double _calculateTotal() {
    return _calculateSubtotal() + _calculateTax() - _discountAmount;
  }

  void _saveAsDraft() {
    // Implement save as draft
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ الفاتورة كمسودة')),
    );
  }

  void _createInvoice() {
    if (_formKey.currentState!.validate()) {
      // Implement create invoice
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إنشاء الفاتورة بنجاح')),
      );
      Navigator.pop(context);
    }
  }
}

class InvoiceItemData {
  String description = '';
  int quantity = 1;
  double unitPrice = 0.0;
}
