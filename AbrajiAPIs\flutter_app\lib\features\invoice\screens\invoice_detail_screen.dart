import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/custom_button.dart';

class InvoiceDetailScreen extends ConsumerWidget {
  final int invoiceId;

  const InvoiceDetailScreen({
    super.key,
    required this.invoiceId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('فاتورة #INV-${invoiceId.toString().padLeft(4, '0')}'),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.edit),
            onPressed: () {
              // Edit invoice
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.share),
            onPressed: () {
              // Share invoice
            },
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'download',
                child: Row(
                  children: [
                    Icon(FontAwesomeIcons.download, size: 16),
                    SizedBox(width: 8),
                    Text('تحميل PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(FontAwesomeIcons.print, size: 16),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(FontAwesomeIcons.copy, size: 16),
                    SizedBox(width: 8),
                    Text('نسخ'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice Header
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'فاتورة #INV-${invoiceId.toString().padLeft(4, '0')}',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'تاريخ الإصدار: 2024-01-15',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            'تاريخ الاستحقاق: 2024-02-15',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      _buildStatusBadge('pending'),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Customer Info
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات العميل',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow(FontAwesomeIcons.user, 'الاسم', 'أحمد محمد'),
                  _buildInfoRow(FontAwesomeIcons.envelope, 'البريد الإلكتروني', '<EMAIL>'),
                  _buildInfoRow(FontAwesomeIcons.phone, 'الهاتف', '+966501234567'),
                  _buildInfoRow(FontAwesomeIcons.mapMarkerAlt, 'العنوان', 'الرياض، المملكة العربية السعودية'),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Invoice Items
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'عناصر الفاتورة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // Items List
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 3,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      return _buildInvoiceItem(
                        'خدمة رقم ${index + 1}',
                        1,
                        500.0,
                        500.0,
                      );
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Invoice Summary
            CustomCard(
              child: Column(
                children: [
                  _buildSummaryRow('المجموع الفرعي', '1,500.00 ر.س'),
                  _buildSummaryRow('الضريبة (15%)', '225.00 ر.س'),
                  _buildSummaryRow('الخصم', '0.00 ر.س'),
                  const Divider(thickness: 2),
                  _buildSummaryRow(
                    'المجموع الكلي',
                    '1,725.00 ر.س',
                    isTotal: true,
                  ),
                  const SizedBox(height: 8),
                  _buildSummaryRow('المدفوع', '0.00 ر.س'),
                  _buildSummaryRow(
                    'المتبقي',
                    '1,725.00 ر.س',
                    color: AppTheme.errorColor,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Payment History
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تاريخ المدفوعات',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Center(
                    child: Text(
                      'لا توجد مدفوعات بعد',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'إضافة دفعة',
                    onPressed: () {
                      _showAddPaymentDialog(context);
                    },
                    icon: FontAwesomeIcons.plus,
                    backgroundColor: AppTheme.successColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SecondaryButton(
                    text: 'إرسال للعميل',
                    onPressed: () {
                      // Send to customer
                    },
                    icon: FontAwesomeIcons.paperPlane,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    String text;
    
    switch (status) {
      case 'paid':
        color = AppTheme.successColor;
        text = 'مدفوعة';
        break;
      case 'pending':
        color = AppTheme.warningColor;
        text = 'معلقة';
        break;
      case 'overdue':
        color = AppTheme.errorColor;
        text = 'متأخرة';
        break;
      case 'cancelled':
        color = Colors.grey;
        text = 'ملغية';
        break;
      default:
        color = Colors.grey;
        text = 'غير محدد';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceItem(String description, int quantity, double unitPrice, double total) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              description,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              quantity.toString(),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              '${unitPrice.toStringAsFixed(2)} ر.س',
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              '${total.toStringAsFixed(2)} ر.س',
              textAlign: TextAlign.end,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: color,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              fontSize: isTotal ? 16 : 14,
              color: color ?? (isTotal ? AppTheme.primaryColor : null),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddPaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة دفعة'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم إضافة نموذج إضافة الدفعة هنا'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }
}
