import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../core/theme/app_theme.dart';
import '../../dashboard/screens/dashboard_screen.dart';
import '../../invoice/screens/invoices_screen.dart';
import '../../transaction/screens/transactions_screen.dart';
import '../../wallet/screens/wallet_screen.dart';
import '../../settings/screens/settings_screen.dart';

final selectedIndexProvider = StateProvider<int>((ref) => 0);

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(selectedIndexProvider);

    final screens = [
      const DashboardScreen(),
      const InvoicesScreen(),
      const TransactionsScreen(),
      const WalletScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      body: IndexedStack(
        index: selectedIndex,
        children: screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: selectedIndex,
        onTap: (index) {
          ref.read(selectedIndexProvider.notifier).state = index;
        },
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        selectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 12,
        ),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.chartLine),
            activeIcon: Icon(FontAwesomeIcons.chartLine),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.fileInvoice),
            activeIcon: Icon(FontAwesomeIcons.fileInvoice),
            label: 'الفواتير',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.exchangeAlt),
            activeIcon: Icon(FontAwesomeIcons.exchangeAlt),
            label: 'المعاملات',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.wallet),
            activeIcon: Icon(FontAwesomeIcons.wallet),
            label: 'المحفظة',
          ),
          BottomNavigationBarItem(
            icon: Icon(FontAwesomeIcons.cog),
            activeIcon: Icon(FontAwesomeIcons.cog),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }
}
