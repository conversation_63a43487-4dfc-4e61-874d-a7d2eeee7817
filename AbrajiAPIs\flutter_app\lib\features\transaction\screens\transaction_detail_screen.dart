import 'package:flutter/material.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_card.dart';

class TransactionDetailScreen extends StatelessWidget {
  final int transactionId;

  const TransactionDetailScreen({
    super.key,
    required this.transactionId,
  });

  @override
  Widget build(BuildContext context) {
    // Mock data - replace with actual data
    final isIncome = transactionId % 3 == 1;
    final isExpense = transactionId % 3 == 2;

    String type;
    IconData icon;
    Color color;
    String amount;

    if (isIncome) {
      type = 'دخل';
      icon = Icons.arrow_upward;
      color = AppTheme.successColor;
      amount = '+${(500 + transactionId * 100).toStringAsFixed(2)} ر.س';
    } else if (isExpense) {
      type = 'مصروف';
      icon = Icons.arrow_downward;
      color = AppTheme.errorColor;
      amount = '-${(200 + transactionId * 50).toStringAsFixed(2)} ر.س';
    } else {
      type = 'تحويل';
      icon = Icons.swap_horiz;
      color = AppTheme.primaryColor;
      amount = '${(300 + transactionId * 75).toStringAsFixed(2)} ر.س';
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('معاملة #TXN-${transactionId.toString().padLeft(4, '0')}'),
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 16),
                    SizedBox(width: 8),
                    Text('نسخ'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Transaction Header
            CustomCard(
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    amount,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: color.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      type,
                      style: TextStyle(
                        color: color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Transaction Details
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل المعاملة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    Icons.tag,
                    'رقم المعاملة',
                    'TXN-${transactionId.toString().padLeft(4, '0')}',
                  ),
                  _buildDetailRow(
                    Icons.calendar_today,
                    'التاريخ',
                    '2024-01-15 14:30',
                  ),
                  _buildDetailRow(
                    Icons.category,
                    'النوع',
                    type,
                  ),
                  _buildDetailRow(
                    Icons.check_circle,
                    'الحالة',
                    'مكتملة',
                    valueColor: AppTheme.successColor,
                  ),
                  _buildDetailRow(
                    Icons.credit_card,
                    'طريقة الدفع',
                    'تحويل بنكي',
                  ),
                  if (!isExpense)
                    _buildDetailRow(
                      Icons.person,
                      'من',
                      'أحمد محمد',
                    ),
                  if (!isIncome)
                    _buildDetailRow(
                      Icons.person_outline,
                      'إلى',
                      'شركة الخدمات',
                    ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Description
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الوصف',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'وصف تفصيلي للمعاملة رقم $transactionId. هذا النص يوضح طبيعة المعاملة والغرض منها.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                          height: 1.5,
                        ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Related Information
            if (isIncome || isExpense) ...[
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isIncome ? 'معلومات الدافع' : 'معلومات المستلم',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 16),
                    _buildDetailRow(
                      Icons.person,
                      'الاسم',
                      isIncome ? 'أحمد محمد' : 'شركة الخدمات',
                    ),
                    _buildDetailRow(
                      Icons.email,
                      'البريد الإلكتروني',
                      isIncome ? '<EMAIL>' : '<EMAIL>',
                    ),
                    _buildDetailRow(
                      Icons.phone,
                      'الهاتف',
                      '+966501234567',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Transaction Timeline
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سجل المعاملة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  _buildTimelineItem(
                    'تم إنشاء المعاملة',
                    '2024-01-15 14:30',
                    Icons.add,
                    AppTheme.primaryColor,
                    isCompleted: true,
                  ),
                  _buildTimelineItem(
                    'تم تأكيد المعاملة',
                    '2024-01-15 14:31',
                    Icons.check,
                    AppTheme.warningColor,
                    isCompleted: true,
                  ),
                  _buildTimelineItem(
                    'تم اكتمال المعاملة',
                    '2024-01-15 14:32',
                    Icons.check_circle,
                    AppTheme.successColor,
                    isCompleted: true,
                    isLast: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value,
      {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(
    String title,
    String time,
    IconData icon,
    Color color, {
    bool isCompleted = false,
    bool isLast = false,
  }) {
    return Row(
      children: [
        Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isCompleted ? color : Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 16,
                color: isCompleted ? Colors.white : Colors.grey[600],
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 30,
                color: isCompleted ? color : Colors.grey[300],
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: isCompleted ? Colors.black : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 2),
              Text(
                time,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              if (!isLast) const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }
}
