; Optimized PHP Configuration for AbrajiAPIs
; Copy relevant sections to your C:\xampp\php\php.ini

[PHP]
; Engine Settings
engine = On
short_open_tag = Off
precision = 14
output_buffering = 4096
zlib.output_compression = Off
implicit_flush = Off
unserialize_callback_func =
serialize_precision = -1
disable_functions =
disable_classes =
zend.enable_gc = On
zend.exception_ignore_args = On

; Resource Limits
max_execution_time = 300
max_input_time = 300
memory_limit = 512M
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20

; Error handling and logging
log_errors = On
log_errors_max_len = 1024
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
display_errors = On
display_startup_errors = On
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Data Handling
variables_order = "GPCS"
request_order = "GP"
register_argc_argv = Off
auto_globals_jit = On

; Paths and Directories
include_path = ".;C:\xampp\php\PEAR"
doc_root =
user_dir =
extension_dir = "C:\xampp\php\ext"
sys_temp_dir = "C:\xampp\tmp"

; File Uploads
file_uploads = On
upload_tmp_dir = "C:\xampp\tmp"

; Fopen wrappers
allow_url_fopen = On
allow_url_include = Off
default_socket_timeout = 60

; Dynamic Extensions (Required for Laravel)
extension=bz2
extension=curl
extension=fileinfo
extension=gd
extension=gettext
extension=gmp
extension=intl
extension=imap
extension=ldap
extension=mbstring
extension=exif
extension=mysqli
extension=oci8_12c
extension=odbc
extension=openssl
extension=pdo_firebird
extension=pdo_mysql
extension=pdo_oci
extension=pdo_odbc
extension=pdo_pgsql
extension=pdo_sqlite
extension=pgsql
extension=shmop
extension=snmp
extension=soap
extension=sockets
extension=sodium
extension=sqlite3
extension=tidy
extension=xmlrpc
extension=xsl
extension=zip

; Module Settings

[CLI Server]
cli_server.color = On

[Date]
date.timezone = "UTC"

[filter]
filter.default = unsafe_raw
filter.default_flags =

[iconv]
iconv.input_encoding =
iconv.internal_encoding =
iconv.output_encoding =

[intl]
intl.default_locale =
intl.error_level = E_WARNING
intl.use_exceptions = 0

[sqlite3]
sqlite3.extension_dir =

[Pcre]
pcre.backtrack_limit = 100000
pcre.recursion_limit = 100000
pcre.jit = 1

[Pdo]
pdo_mysql.default_socket =

[Pdo_mysql]
pdo_mysql.default_socket =

[Phar]
phar.readonly = On
phar.require_hash = On
phar.cache_list =

[mail function]
SMTP = localhost
smtp_port = 25
mail.add_x_header = Off

[ODBC]
odbc.allow_persistent = On
odbc.check_persistent = On
odbc.max_persistent = -1
odbc.max_links = -1
odbc.defaultlrl = 4096
odbc.defaultbinmode = 1

[Interbase]
ibase.allow_persistent = 1
ibase.max_persistent = -1
ibase.max_links = -1
ibase.timestampformat = "%Y-%m-%d %H:%M:%S"
ibase.dateformat = "%Y-%m-%d"
ibase.timeformat = "%H:%M:%S"

[MySQLi]
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.default_port = 3306
mysqli.default_socket =
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off

[mysqlnd]
mysqlnd.collect_statistics = On
mysqlnd.collect_memory_statistics = Off

[OCI8]
oci8.max_persistent = -1
oci8.persistent_timeout = -1
oci8.ping_interval = 60
oci8.connection_class =
oci8.events = Off
oci8.statement_cache_size = 20
oci8.default_prefetch = 100
oci8.old_oci_close_semantics = Off

[PostgreSQL]
pgsql.allow_persistent = On
pgsql.auto_reset_persistent = Off
pgsql.max_persistent = -1
pgsql.max_links = -1
pgsql.ignore_notice = 0
pgsql.log_notice = 0

[bcmath]
bcmath.scale = 0

[browscap]
; Commented out to avoid file not found errors
; browscap = "C:\xampp\php\extras\browscap.ini"

[Session]
session.save_handler = files
session.save_path = "C:\xampp\tmp"
session.use_strict_mode = 0
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly =
session.cookie_samesite =
session.serialize_handler = php
session.gc_probability = 0
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.referer_check =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.sid_length = 26
session.trans_sid_tags = "a=href,area=href,frame=src,form="
session.sid_bits_per_character = 5

[Assertion]
zend.assertions = 1
assert.active = 1
assert.exception = 0
assert.warning = 1
assert.bail = 0
assert.callback = 0
assert.quiet_eval = 0

[COM]
com.typelib_file =
com.allow_dcom = true
com.autoregister_typelib = true
com.autoregister_casesensitive = false
com.autoregister_verbose = true
com.code_page =

[mbstring]
mbstring.language = English
mbstring.internal_encoding =
mbstring.http_input =
mbstring.http_output =
mbstring.encoding_translation = Off
mbstring.detect_order = auto
mbstring.substitute_character = none
mbstring.func_overload = 0
mbstring.strict_detection = On
mbstring.http_output_conv_mimetype =

[gd]
gd.jpeg_ignore_warning = 1

[exif]
exif.encode_unicode = ISO-8859-15
exif.decode_unicode_motorola = UCS-2BE
exif.decode_unicode_intel = UCS-2LE
exif.encode_jis =
exif.decode_jis_motorola = JIS
exif.decode_jis_intel = JIS

[Tidy]
tidy.clean_output = Off

[soap]
soap.wsdl_cache_enabled = 1
soap.wsdl_cache_dir = "C:\xampp\tmp"
soap.wsdl_cache_ttl = 86400
soap.wsdl_cache_limit = 5

[sysvshm]
sysvshm.init_mem = 10000

[ldap]
ldap.max_links = -1

[dba]
dba.default_handler =

[opcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

[curl]
curl.cainfo = "C:\xampp\apache\bin\curl-ca-bundle.crt"

[openssl]
openssl.cafile = "C:\xampp\apache\bin\curl-ca-bundle.crt"
openssl.capath = "C:\xampp\apache\bin"
