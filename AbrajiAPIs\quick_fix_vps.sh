#!/bin/bash

# Quick fix script for common VPS setup issues
# Run this if vps_setup_ubuntu.sh encounters problems

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

print_header "VPS Quick Fix Script"

# Get database password
read -s -p "Enter database password for abrajiapis user: " DB_PASSWORD
echo ""

# Fix 1: MySQL Authentication Issue
print_status "Fixing MySQL authentication..."
systemctl stop mysql 2>/dev/null || true

# Kill any existing MySQL processes
pkill mysqld 2>/dev/null || true
pkill mysqld_safe 2>/dev/null || true
sleep 3

# Start MySQL in safe mode
print_status "Starting MySQL in safe mode..."
mysqld_safe --skip-grant-tables --skip-networking &
SAFE_PID=$!
sleep 15

# Reset root password
print_status "Resetting MySQL root password..."
mysql -u root << EOF
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';
FLUSH PRIVILEGES;
EXIT;
EOF

# Stop safe mode
print_status "Stopping safe mode..."
kill $SAFE_PID 2>/dev/null || true
pkill mysqld_safe 2>/dev/null || true
pkill mysqld 2>/dev/null || true
sleep 5

# Start MySQL normally
systemctl start mysql
systemctl enable mysql
sleep 5

# Test and create database
print_status "Creating database and user..."
mysql -u root -p$DB_PASSWORD << EOF
CREATE DATABASE IF NOT EXISTS abrajiapis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'abrajiapis'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON abrajiapis.* TO 'abrajiapis'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

# Test connections
if mysql -u root -p$DB_PASSWORD -e "SELECT 1;" &>/dev/null; then
    print_status "✅ MySQL root connection successful"
else
    print_error "❌ MySQL root connection failed"
fi

if mysql -u abrajiapis -p$DB_PASSWORD -e "USE abrajiapis; SELECT 1;" &>/dev/null; then
    print_status "✅ abrajiapis user connection successful"
else
    print_error "❌ abrajiapis user connection failed"
fi

# Fix 2: PHP-FPM Issues
print_status "Fixing PHP-FPM..."
systemctl stop php8.2-fpm 2>/dev/null || true
systemctl start php8.2-fpm
systemctl enable php8.2-fpm

if systemctl is-active --quiet php8.2-fpm; then
    print_status "✅ PHP-FPM is running"
else
    print_error "❌ PHP-FPM failed to start"
fi

# Fix 3: Nginx Issues
print_status "Fixing Nginx..."
nginx -t 2>/dev/null || print_warning "Nginx configuration has issues"
systemctl restart nginx

if systemctl is-active --quiet nginx; then
    print_status "✅ Nginx is running"
else
    print_error "❌ Nginx failed to start"
fi

# Fix 4: User and Directory Issues
print_status "Fixing user and directories..."
if ! id "abrajiapis" &>/dev/null; then
    adduser --disabled-password --gecos "" abrajiapis
    usermod -aG www-data abrajiapis
    print_status "✅ Created abrajiapis user"
fi

mkdir -p /home/<USER>/AbrajiAPIs
mkdir -p /home/<USER>/backups
chown -R abrajiapis:www-data /home/<USER>/AbrajiAPIs
chown abrajiapis:abrajiapis /home/<USER>/backups

# Fix 5: Create missing scripts
print_status "Creating deployment scripts..."

# Deploy script
cat > /home/<USER>/deploy.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/AbrajiAPIs

echo "Starting deployment..."

# Install/update dependencies
if [ -f "composer.json" ]; then
    composer install --no-dev --optimize-autoloader --no-interaction
    echo "✅ Dependencies installed"
fi

# Generate app key if needed
if [ -f ".env" ] && ! grep -q "APP_KEY=base64:" .env; then
    php artisan key:generate --force
    echo "✅ App key generated"
fi

# Run migrations
if [ -f "artisan" ]; then
    php artisan migrate --force
    echo "✅ Migrations completed"
fi

# Clear and cache config
php artisan config:cache 2>/dev/null || true
php artisan route:cache 2>/dev/null || true
php artisan view:cache 2>/dev/null || true

# Create storage link
php artisan storage:link 2>/dev/null || true

# Set permissions
sudo chown -R abrajiapis:www-data /home/<USER>/AbrajiAPIs
sudo find /home/<USER>/AbrajiAPIs -type f -exec chmod 644 {} \; 2>/dev/null || true
sudo find /home/<USER>/AbrajiAPIs -type d -exec chmod 755 {} \; 2>/dev/null || true
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/storage 2>/dev/null || true
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/bootstrap/cache 2>/dev/null || true

echo "🎉 Deployment completed!"
EOF

chmod +x /home/<USER>/deploy.sh
chown abrajiapis:abrajiapis /home/<USER>/deploy.sh

# Backup script
cat > /home/<USER>/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
PROJECT_DIR="/home/<USER>/AbrajiAPIs"

mkdir -p \$BACKUP_DIR

# Database backup
if command -v mysqldump &> /dev/null; then
    mysqldump -u abrajiapis -p$DB_PASSWORD abrajiapis > \$BACKUP_DIR/database_\$DATE.sql 2>/dev/null && echo "✅ Database backup created"
fi

# Files backup
if [ -d "\$PROJECT_DIR" ]; then
    tar -czf \$BACKUP_DIR/files_\$DATE.tar.gz \$PROJECT_DIR 2>/dev/null && echo "✅ Files backup created"
fi

# Remove old backups (older than 7 days)
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete 2>/dev/null
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete 2>/dev/null

echo "🎉 Backup completed: \$DATE"
EOF

chmod +x /home/<USER>/backup.sh
chown abrajiapis:abrajiapis /home/<USER>/backup.sh

# Fix 6: Create .env template
print_status "Creating .env template..."
cat > /home/<USER>/.env.template << EOF
APP_NAME=AbrajiAPIs
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=abrajiapis
DB_USERNAME=abrajiapis
DB_PASSWORD=$DB_PASSWORD

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# SAS Radius Configuration
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost
EOF

chown abrajiapis:abrajiapis /home/<USER>/.env.template

# Fix 7: Firewall
print_status "Configuring firewall..."
ufw allow OpenSSH 2>/dev/null || true
ufw allow 'Nginx Full' 2>/dev/null || true
ufw --force enable 2>/dev/null || true

# Fix 8: System cleanup
print_status "Cleaning up system..."
apt autoremove -y 2>/dev/null || true
apt autoclean 2>/dev/null || true

print_header "Quick Fix Completed"
print_status ""
print_status "✅ MySQL authentication fixed"
print_status "✅ PHP-FPM restarted"
print_status "✅ Nginx restarted"
print_status "✅ User and directories created"
print_status "✅ Deployment scripts created"
print_status "✅ Environment template created"
print_status "✅ Firewall configured"
print_status ""
print_status "Database credentials:"
print_status "- Database: abrajiapis"
print_status "- Username: abrajiapis"
print_status "- Password: $DB_PASSWORD"
print_status ""
print_status "Next steps:"
print_status "1. Upload your project files to: /home/<USER>/AbrajiAPIs/"
print_status "2. Copy .env.template to .env and configure"
print_status "3. Run: bash /home/<USER>/deploy.sh"
print_status "4. Test your application"
print_status ""
print_header "System is ready! 🚀"
