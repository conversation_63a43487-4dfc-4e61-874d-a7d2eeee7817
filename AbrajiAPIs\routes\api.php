<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UsersController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\TransactionsController;

/*
|--------------------------------------------------------------------------
| API Routes - Abraji APIs Implementation
|--------------------------------------------------------------------------
|
| تنفيذ جميع APIs الموجودة في Postman Collection
|
*/

// ================================
// Authentication Routes (غير محمية)
// ================================
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
});

// ================================
// Protected Routes (محمية بـ Sanctum)
// ================================
Route::middleware('auth:sanctum')->group(function () {

    // Auth Routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
    });

    // Dashboard Routes
    Route::prefix('dashboard')->group(function () {
        Route::get('/', [DashboardController::class, 'index']);
        Route::get('cards', [DashboardController::class, 'cards']);
        Route::get('transactions', [DashboardController::class, 'transactions']);
        Route::get('charts', [DashboardController::class, 'charts']);
        Route::get('activities', [DashboardController::class, 'activities']);
    });

    // Users Routes
    Route::prefix('users')->group(function () {
        Route::get('/', [UsersController::class, 'index']);
        Route::post('/', [UsersController::class, 'store']);
        Route::get('online', [UsersController::class, 'onlineUsers']);
        Route::get('{id}', [UsersController::class, 'show']);
        Route::put('{id}', [UsersController::class, 'update']);
        Route::delete('{id}', [UsersController::class, 'destroy']);
        Route::post('{id}/activate', [UsersController::class, 'activate']);
        Route::post('{id}/disconnect', [UsersController::class, 'disconnect']);
        Route::get('{id}/stats', [UsersController::class, 'getUserStats']);
    });

    // Transactions Routes
    Route::prefix('transactions')->group(function () {
        Route::get('/', [TransactionsController::class, 'index']);
        Route::post('/', [TransactionsController::class, 'store']);
        Route::get('stats', [TransactionsController::class, 'stats']);
        Route::get('export', [TransactionsController::class, 'export']);
        Route::get('{id}', [TransactionsController::class, 'show']);
        Route::put('{id}', [TransactionsController::class, 'update']);
        Route::delete('{id}', [TransactionsController::class, 'destroy']);
    });

});

