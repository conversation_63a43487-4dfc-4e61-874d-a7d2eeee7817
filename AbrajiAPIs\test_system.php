<?php
/**
 * System Test Script for AbrajiAPIs
 * Run this script to test if everything is working correctly
 */

echo "🔧 AbrajiAPIs System Test\n";
echo "========================\n\n";

// Test 1: PHP Version
echo "1. Testing PHP Version...\n";
$phpVersion = phpversion();
echo "   PHP Version: {$phpVersion}\n";
if (version_compare($phpVersion, '8.2.0', '>=')) {
    echo "   ✅ PHP version is compatible\n\n";
} else {
    echo "   ❌ PHP version must be 8.2 or higher\n\n";
}

// Test 2: Required Extensions
echo "2. Testing Required Extensions...\n";
$requiredExtensions = [
    'curl', 'fileinfo', 'mbstring', 'openssl', 'pdo', 'pdo_sqlite', 
    'pdo_mysql', 'tokenizer', 'xml', 'zip', 'json', 'gd'
];

$missingExtensions = [];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext}\n";
    } else {
        echo "   ❌ {$ext} (missing)\n";
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    echo "   ✅ All required extensions are loaded\n\n";
} else {
    echo "   ❌ Missing extensions: " . implode(', ', $missingExtensions) . "\n\n";
}

// Test 3: File Permissions
echo "3. Testing File Permissions...\n";
$writableDirectories = [
    'storage/app',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($writableDirectories as $dir) {
    if (is_writable($dir)) {
        echo "   ✅ {$dir} is writable\n";
    } else {
        echo "   ❌ {$dir} is not writable\n";
    }
}
echo "\n";

// Test 4: Environment File
echo "4. Testing Environment Configuration...\n";
if (file_exists('.env')) {
    echo "   ✅ .env file exists\n";
    
    // Load .env file
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    $envVars = [];
    
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            list($key, $value) = explode('=', $line, 2);
            $envVars[trim($key)] = trim($value);
        }
    }
    
    // Check important variables
    $importantVars = ['APP_NAME', 'APP_KEY', 'DB_CONNECTION', 'API_DOMAIN'];
    foreach ($importantVars as $var) {
        if (isset($envVars[$var]) && !empty($envVars[$var])) {
            echo "   ✅ {$var} is set\n";
        } else {
            echo "   ❌ {$var} is not set or empty\n";
        }
    }
} else {
    echo "   ❌ .env file does not exist\n";
}
echo "\n";

// Test 5: Database Connection
echo "5. Testing Database Connection...\n";
try {
    if (file_exists('database/database.sqlite')) {
        echo "   ✅ SQLite database file exists\n";
        
        $pdo = new PDO('sqlite:database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Test if tables exist
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('migrations', $tables)) {
            echo "   ✅ Database is migrated\n";
        } else {
            echo "   ❌ Database needs migration\n";
        }
        
        if (in_array('settings', $tables)) {
            echo "   ✅ Settings table exists\n";
        } else {
            echo "   ❌ Settings table does not exist\n";
        }
        
    } else {
        echo "   ❌ SQLite database file does not exist\n";
    }
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: Composer Dependencies
echo "6. Testing Composer Dependencies...\n";
if (file_exists('vendor/autoload.php')) {
    echo "   ✅ Composer dependencies are installed\n";
    
    // Check if Laravel is installed
    if (file_exists('vendor/laravel/framework')) {
        echo "   ✅ Laravel framework is installed\n";
    } else {
        echo "   ❌ Laravel framework is not installed\n";
    }
    
    // Check if Guzzle is installed
    if (file_exists('vendor/guzzlehttp/guzzle')) {
        echo "   ✅ Guzzle HTTP client is installed\n";
    } else {
        echo "   ❌ Guzzle HTTP client is not installed\n";
    }
} else {
    echo "   ❌ Composer dependencies are not installed\n";
}
echo "\n";

// Test 7: Module Status
echo "7. Testing Module Status...\n";
if (file_exists('modules_statuses.json')) {
    $moduleStatus = json_decode(file_get_contents('modules_statuses.json'), true);
    
    $requiredModules = ['Authentication', 'Settings', 'Wallet', 'Users'];
    foreach ($requiredModules as $module) {
        if (isset($moduleStatus[$module]) && $moduleStatus[$module]) {
            echo "   ✅ {$module} module is enabled\n";
        } else {
            echo "   ❌ {$module} module is disabled or missing\n";
        }
    }
} else {
    echo "   ❌ modules_statuses.json file does not exist\n";
}
echo "\n";

// Test 8: Settings Module Files
echo "8. Testing Settings Module Files...\n";
$settingsFiles = [
    'Modules/Settings/app/Models/Setting.php',
    'Modules/Settings/app/Services/SettingsService.php',
    'Modules/Settings/app/Http/Controllers/SettingsController.php',
    'Modules/Settings/routes/api.php',
    'app/Services/SasRadiusUrlService.php'
];

foreach ($settingsFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file}\n";
    } else {
        echo "   ❌ {$file} (missing)\n";
    }
}
echo "\n";

// Summary
echo "🎯 Test Summary\n";
echo "===============\n";

$allGood = empty($missingExtensions) && 
           file_exists('.env') && 
           file_exists('database/database.sqlite') && 
           file_exists('vendor/autoload.php') &&
           file_exists('Modules/Settings/app/Models/Setting.php');

if ($allGood) {
    echo "✅ System appears to be ready!\n";
    echo "\nNext steps:\n";
    echo "1. Run: php artisan serve\n";
    echo "2. Visit: http://localhost:8000\n";
    echo "3. Test API: http://localhost:8000/api/settings\n";
} else {
    echo "❌ System needs attention. Please fix the issues above.\n";
    echo "\nCommon fixes:\n";
    echo "1. Run: composer install\n";
    echo "2. Run: php artisan migrate\n";
    echo "3. Check PHP extensions in php.ini\n";
    echo "4. Ensure file permissions are correct\n";
}

echo "\n🔧 Test completed!\n";
?>
