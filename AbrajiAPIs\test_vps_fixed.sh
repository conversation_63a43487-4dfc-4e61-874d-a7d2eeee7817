#!/bin/bash

# Simple VPS Test Script for AbrajiAPIs
# Run this after vps_setup_ubuntu_fixed.sh

echo "🔍 Testing VPS Setup..."
echo "======================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

pass() { echo -e "${GREEN}✅ PASS:${NC} $1"; }
fail() { echo -e "${RED}❌ FAIL:${NC} $1"; }
warn() { echo -e "${YELLOW}⚠️  WARN:${NC} $1"; }

# Test 1: PHP
echo ""
echo "Testing PHP..."
if php8.2 -v &>/dev/null; then
    pass "PHP 8.2 is installed"
    
    # Test extensions
    EXTENSIONS=("curl" "mysql" "mbstring" "xml" "zip" "gd")
    for ext in "${EXTENSIONS[@]}"; do
        if php8.2 -m | grep -q "$ext"; then
            pass "PHP extension: $ext"
        else
            fail "PHP extension missing: $ext"
        fi
    done
else
    fail "PHP 8.2 not found"
fi

# Test 2: MySQL
echo ""
echo "Testing MySQL..."
if systemctl is-active --quiet mysql; then
    pass "MySQL service is running"
    
    # Test if we can connect (basic test)
    if mysql -e "SELECT 1;" &>/dev/null 2>&1; then
        pass "MySQL connection works"
    else
        warn "MySQL connection test skipped (credentials needed)"
    fi
else
    fail "MySQL service is not running"
fi

# Test 3: Nginx
echo ""
echo "Testing Nginx..."
if systemctl is-active --quiet nginx; then
    pass "Nginx service is running"
    
    if nginx -t &>/dev/null; then
        pass "Nginx configuration is valid"
    else
        fail "Nginx configuration has errors"
    fi
else
    fail "Nginx service is not running"
fi

# Test 4: PHP-FPM
echo ""
echo "Testing PHP-FPM..."
if systemctl is-active --quiet php8.2-fpm; then
    pass "PHP-FPM service is running"
    
    if [ -S "/var/run/php/php8.2-fpm.sock" ]; then
        pass "PHP-FPM socket exists"
    else
        fail "PHP-FPM socket not found"
    fi
else
    fail "PHP-FPM service is not running"
fi

# Test 5: Composer
echo ""
echo "Testing Composer..."
if command -v composer &>/dev/null; then
    pass "Composer is installed"
else
    fail "Composer not found"
fi

# Test 6: User and directories
echo ""
echo "Testing User Setup..."
if id "abrajiapis" &>/dev/null; then
    pass "User 'abrajiapis' exists"
    
    if [ -d "/home/<USER>/AbrajiAPIs" ]; then
        pass "Project directory exists"
    else
        fail "Project directory missing"
    fi
    
    if [ -f "/home/<USER>/deploy.sh" ]; then
        pass "Deploy script exists"
    else
        fail "Deploy script missing"
    fi
else
    fail "User 'abrajiapis' does not exist"
fi

# Test 7: Firewall
echo ""
echo "Testing Firewall..."
if ufw status | grep -q "Status: active"; then
    pass "UFW firewall is active"
else
    warn "UFW firewall is not active"
fi

# Test 8: Web server response
echo ""
echo "Testing Web Server..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost 2>/dev/null || echo "000")
if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "404" ] || [ "$HTTP_CODE" = "403" ]; then
    pass "Web server responds (HTTP $HTTP_CODE)"
else
    warn "Web server may not be responding correctly (HTTP $HTTP_CODE)"
fi

# Test 9: System resources
echo ""
echo "Checking System Resources..."
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3*100/$2}')
DISK_USAGE=$(df / | awk 'NR==2{printf "%.1f", $3*100/$2}')

echo "💾 Disk usage: ${DISK_USAGE}%"
echo "🧠 Memory usage: ${MEMORY_USAGE}%"

if (( $(echo "$DISK_USAGE < 80" | bc -l) )); then
    pass "Disk usage is acceptable"
else
    warn "High disk usage: ${DISK_USAGE}%"
fi

if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
    pass "Memory usage is acceptable"
else
    warn "High memory usage: ${MEMORY_USAGE}%"
fi

echo ""
echo "======================="
echo "🎯 Test Summary Complete"
echo "======================="
echo ""
echo "If all tests show ✅ PASS, your VPS is ready!"
echo "If you see ❌ FAIL, please fix those issues."
echo "⚠️  WARN items are optional but recommended."
echo ""
echo "Next: Upload your AbrajiAPIs project and run deploy.sh"
