#!/bin/bash

# Test script for VPS setup validation
# Run this after vps_setup_ubuntu.sh to verify everything is working

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ℹ️  INFO]${NC} $1"
}

echo "=========================================="
echo "  VPS Setup Validation Test"
echo "=========================================="

# Test 1: Check if running as root
if [[ $EUID -eq 0 ]]; then
    print_status "Running as root user"
else
    print_error "Not running as root - some tests may fail"
fi

# Test 2: Check PHP installation
print_info "Testing PHP installation..."
if command -v php8.2 &> /dev/null; then
    PHP_VERSION=$(php8.2 -v | head -n1)
    print_status "PHP 8.2 installed: $PHP_VERSION"
    
    # Check PHP extensions
    REQUIRED_EXTENSIONS=("curl" "mysql" "mbstring" "xml" "zip" "gd" "intl" "bcmath")
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if php8.2 -m | grep -q "$ext"; then
            print_status "PHP extension $ext is loaded"
        else
            print_error "PHP extension $ext is missing"
        fi
    done
else
    print_error "PHP 8.2 not found"
fi

# Test 3: Check MySQL
print_info "Testing MySQL installation..."
if systemctl is-active --quiet mysql; then
    print_status "MySQL service is running"
    
    # Test MySQL connection (if credentials are available)
    if [ -f "/root/.my.cnf" ]; then
        if mysql -e "SELECT 1;" &>/dev/null; then
            print_status "MySQL root connection successful"
            
            # Check if abrajiapis database exists
            if mysql -e "USE abrajiapis; SELECT 1;" &>/dev/null; then
                print_status "Database 'abrajiapis' exists and accessible"
            else
                print_error "Database 'abrajiapis' not found or not accessible"
            fi
        else
            print_error "MySQL root connection failed"
        fi
    else
        print_warning "MySQL credentials not found in /root/.my.cnf"
    fi
else
    print_error "MySQL service is not running"
fi

# Test 4: Check Nginx
print_info "Testing Nginx installation..."
if systemctl is-active --quiet nginx; then
    print_status "Nginx service is running"
    
    # Test Nginx configuration
    if nginx -t &>/dev/null; then
        print_status "Nginx configuration is valid"
    else
        print_error "Nginx configuration has errors"
    fi
    
    # Check if abrajiapis site is enabled
    if [ -f "/etc/nginx/sites-enabled/abrajiapis" ]; then
        print_status "AbrajiAPIs site is enabled in Nginx"
    else
        print_error "AbrajiAPIs site is not enabled in Nginx"
    fi
else
    print_error "Nginx service is not running"
fi

# Test 5: Check PHP-FPM
print_info "Testing PHP-FPM..."
if systemctl is-active --quiet php8.2-fpm; then
    print_status "PHP-FPM service is running"
    
    # Check if PHP-FPM socket exists
    if [ -S "/var/run/php/php8.2-fpm.sock" ]; then
        print_status "PHP-FPM socket exists"
    else
        print_error "PHP-FPM socket not found"
    fi
else
    print_error "PHP-FPM service is not running"
fi

# Test 6: Check Composer
print_info "Testing Composer installation..."
if command -v composer &> /dev/null; then
    COMPOSER_VERSION=$(composer --version)
    print_status "Composer installed: $COMPOSER_VERSION"
else
    print_error "Composer not found"
fi

# Test 7: Check user and directories
print_info "Testing user and directory setup..."
if id "abrajiapis" &>/dev/null; then
    print_status "User 'abrajiapis' exists"
    
    if [ -d "/home/<USER>/AbrajiAPIs" ]; then
        print_status "Project directory exists"
        
        # Check ownership
        OWNER=$(stat -c '%U:%G' /home/<USER>/AbrajiAPIs)
        if [ "$OWNER" = "abrajiapis:www-data" ]; then
            print_status "Project directory has correct ownership"
        else
            print_warning "Project directory ownership: $OWNER (expected: abrajiapis:www-data)"
        fi
    else
        print_error "Project directory /home/<USER>/AbrajiAPIs does not exist"
    fi
else
    print_error "User 'abrajiapis' does not exist"
fi

# Test 8: Check scripts
print_info "Testing deployment scripts..."
if [ -f "/home/<USER>/deploy.sh" ] && [ -x "/home/<USER>/deploy.sh" ]; then
    print_status "Deploy script exists and is executable"
else
    print_error "Deploy script missing or not executable"
fi

if [ -f "/home/<USER>/backup.sh" ] && [ -x "/home/<USER>/backup.sh" ]; then
    print_status "Backup script exists and is executable"
else
    print_error "Backup script missing or not executable"
fi

# Test 9: Check firewall
print_info "Testing firewall configuration..."
if ufw status | grep -q "Status: active"; then
    print_status "UFW firewall is active"
    
    if ufw status | grep -q "Nginx Full"; then
        print_status "Nginx Full rule is enabled"
    else
        print_warning "Nginx Full rule not found"
    fi
    
    if ufw status | grep -q "OpenSSH"; then
        print_status "OpenSSH rule is enabled"
    else
        print_warning "OpenSSH rule not found"
    fi
else
    print_warning "UFW firewall is not active"
fi

# Test 10: Check SSL certificate (if domain is configured)
print_info "Testing SSL certificate..."
if [ -f "/etc/nginx/sites-available/abrajiapis" ]; then
    DOMAIN=$(grep "server_name" /etc/nginx/sites-available/abrajiapis | head -n1 | awk '{print $2}' | sed 's/;//')
    
    if [ "$DOMAIN" != "abraji" ] && [ "$DOMAIN" != "localhost" ]; then
        if command -v certbot &> /dev/null; then
            print_status "Certbot is installed"
            
            if certbot certificates 2>/dev/null | grep -q "$DOMAIN"; then
                print_status "SSL certificate exists for $DOMAIN"
            else
                print_warning "No SSL certificate found for $DOMAIN"
            fi
        else
            print_error "Certbot not installed"
        fi
    else
        print_info "Using local domain ($DOMAIN) - SSL not applicable"
    fi
fi

# Test 11: Check system resources
print_info "Checking system resources..."
MEMORY=$(free -m | awk 'NR==2{printf "%.1f", $3*100/$2}')
DISK=$(df / | awk 'NR==2{printf "%.1f", $3*100/$2}')
LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

print_info "Memory usage: ${MEMORY}%"
print_info "Disk usage: ${DISK}%"
print_info "Load average: ${LOAD}"

if (( $(echo "$MEMORY < 80" | bc -l) )); then
    print_status "Memory usage is acceptable"
else
    print_warning "High memory usage: ${MEMORY}%"
fi

if (( $(echo "$DISK < 80" | bc -l) )); then
    print_status "Disk usage is acceptable"
else
    print_warning "High disk usage: ${DISK}%"
fi

# Test 12: Test HTTP response
print_info "Testing HTTP response..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|404\|403"; then
    print_status "Web server is responding"
else
    print_warning "Web server may not be responding correctly"
fi

echo ""
echo "=========================================="
echo "  Validation Test Completed"
echo "=========================================="
echo ""
echo "Summary:"
echo "- Run this test after completing VPS setup"
echo "- Green checkmarks (✅) indicate successful setup"
echo "- Yellow warnings (⚠️) may need attention"
echo "- Red errors (❌) require fixing"
echo ""
echo "Next steps if all tests pass:"
echo "1. Upload your AbrajiAPIs project files"
echo "2. Configure .env file"
echo "3. Run deployment script"
echo "4. Test your application"
echo "=========================================="
