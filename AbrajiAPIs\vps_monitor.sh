#!/bin/bash

# AbrajiAPIs VPS Monitoring and Maintenance Script
# Run this script regularly to monitor system health

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/home/<USER>/AbrajiAPIs"
LOG_FILE="/home/<USER>/monitor.log"
ALERT_EMAIL="<EMAIL>"  # Change this to your email

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1" >> "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARNING] $1" >> "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >> "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to send alert email
send_alert() {
    local subject="$1"
    local message="$2"
    
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    fi
}

# Function to check disk space
check_disk_space() {
    print_status "Checking disk space..."
    
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -gt 90 ]; then
        print_error "Disk space critical: ${usage}% used"
        send_alert "AbrajiAPIs: Disk Space Critical" "Disk usage is at ${usage}%. Please free up space immediately."
        return 1
    elif [ "$usage" -gt 80 ]; then
        print_warning "Disk space warning: ${usage}% used"
        send_alert "AbrajiAPIs: Disk Space Warning" "Disk usage is at ${usage}%. Consider cleaning up files."
    else
        print_status "Disk space OK: ${usage}% used"
    fi
    
    return 0
}

# Function to check memory usage
check_memory() {
    print_status "Checking memory usage..."
    
    local mem_info=$(free | grep Mem)
    local total=$(echo $mem_info | awk '{print $2}')
    local used=$(echo $mem_info | awk '{print $3}')
    local usage=$((used * 100 / total))
    
    if [ "$usage" -gt 90 ]; then
        print_error "Memory usage critical: ${usage}%"
        send_alert "AbrajiAPIs: Memory Critical" "Memory usage is at ${usage}%. System may become unstable."
        return 1
    elif [ "$usage" -gt 80 ]; then
        print_warning "Memory usage high: ${usage}%"
    else
        print_status "Memory usage OK: ${usage}%"
    fi
    
    return 0
}

# Function to check CPU load
check_cpu_load() {
    print_status "Checking CPU load..."
    
    local load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_percentage=$(echo "$load * 100 / $cpu_cores" | bc -l | cut -d. -f1)
    
    if [ "$load_percentage" -gt 90 ]; then
        print_error "CPU load critical: ${load} (${load_percentage}%)"
        send_alert "AbrajiAPIs: High CPU Load" "CPU load is ${load} on ${cpu_cores} cores (${load_percentage}%)."
        return 1
    elif [ "$load_percentage" -gt 70 ]; then
        print_warning "CPU load high: ${load} (${load_percentage}%)"
    else
        print_status "CPU load OK: ${load} (${load_percentage}%)"
    fi
    
    return 0
}

# Function to check services
check_services() {
    print_status "Checking services..."
    
    local services=("nginx" "php8.2-fpm" "mysql")
    local failed_services=()
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            print_status "$service is running"
        else
            print_error "$service is not running"
            failed_services+=("$service")
            
            # Try to restart the service
            print_status "Attempting to restart $service..."
            if systemctl restart "$service"; then
                print_status "$service restarted successfully"
            else
                print_error "Failed to restart $service"
            fi
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        send_alert "AbrajiAPIs: Service Failure" "The following services failed: ${failed_services[*]}"
        return 1
    fi
    
    return 0
}

# Function to check website availability
check_website() {
    print_status "Checking website availability..."
    
    local domain=$(grep APP_URL "$PROJECT_DIR/.env" | cut -d'=' -f2 | sed 's/https\?:\/\///')
    
    if [ -z "$domain" ]; then
        print_warning "Could not determine domain from .env file"
        return 0
    fi
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" "https://$domain" --max-time 10)
    
    if [ "$response" = "200" ]; then
        print_status "Website is accessible (HTTP $response)"
    else
        print_error "Website is not accessible (HTTP $response)"
        send_alert "AbrajiAPIs: Website Down" "Website https://$domain returned HTTP $response"
        return 1
    fi
    
    return 0
}

# Function to check database connectivity
check_database() {
    print_status "Checking database connectivity..."
    
    if sudo -u abrajiapis php "$PROJECT_DIR/artisan" migrate:status &>/dev/null; then
        print_status "Database connection OK"
    else
        print_error "Database connection failed"
        send_alert "AbrajiAPIs: Database Error" "Cannot connect to database or run migrations"
        return 1
    fi
    
    return 0
}

# Function to check log file sizes
check_log_sizes() {
    print_status "Checking log file sizes..."
    
    local log_dirs=("$PROJECT_DIR/storage/logs" "/var/log/nginx" "/var/log/mysql")
    
    for log_dir in "${log_dirs[@]}"; do
        if [ -d "$log_dir" ]; then
            local size=$(du -sh "$log_dir" | cut -f1)
            print_status "Log directory $log_dir: $size"
            
            # Check if any single log file is larger than 100MB
            find "$log_dir" -name "*.log" -size +100M -exec basename {} \; | while read large_log; do
                print_warning "Large log file found: $log_dir/$large_log"
            done
        fi
    done
}

# Function to clean up old files
cleanup_old_files() {
    print_status "Cleaning up old files..."
    
    # Clean old Laravel logs (keep last 7 days)
    find "$PROJECT_DIR/storage/logs" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # Clean old backups (keep last 30 days)
    find "/home/<USER>/backups" -name "*.tar.gz" -mtime +30 -delete 2>/dev/null || true
    find "/home/<USER>/backups" -name "*.sql" -mtime +30 -delete 2>/dev/null || true
    
    # Clean temporary files
    find /tmp -name "abrajiapis-*" -mtime +1 -delete 2>/dev/null || true
    
    print_status "Cleanup completed"
}

# Function to update system packages
update_system() {
    print_status "Checking for system updates..."
    
    local updates=$(apt list --upgradable 2>/dev/null | grep -c upgradable || echo "0")
    
    if [ "$updates" -gt 0 ]; then
        print_status "$updates package updates available"
        
        # Only update security packages automatically
        unattended-upgrade -d 2>/dev/null || true
        
        print_status "Security updates applied"
    else
        print_status "System is up to date"
    fi
}

# Function to check SSL certificate expiry
check_ssl_certificate() {
    print_status "Checking SSL certificate..."
    
    local domain=$(grep APP_URL "$PROJECT_DIR/.env" | cut -d'=' -f2 | sed 's/https\?:\/\///')
    
    if [ -z "$domain" ]; then
        return 0
    fi
    
    local expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    
    if [ -n "$expiry_date" ]; then
        local expiry_epoch=$(date -d "$expiry_date" +%s)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [ "$days_until_expiry" -lt 7 ]; then
            print_error "SSL certificate expires in $days_until_expiry days"
            send_alert "AbrajiAPIs: SSL Certificate Expiring" "SSL certificate for $domain expires in $days_until_expiry days"
        elif [ "$days_until_expiry" -lt 30 ]; then
            print_warning "SSL certificate expires in $days_until_expiry days"
        else
            print_status "SSL certificate valid for $days_until_expiry days"
        fi
    fi
}

# Function to generate system report
generate_report() {
    local report_file="/home/<USER>/system_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "AbrajiAPIs System Report - $(date)"
        echo "========================================"
        echo ""
        echo "System Information:"
        echo "- Hostname: $(hostname)"
        echo "- Uptime: $(uptime)"
        echo "- Kernel: $(uname -r)"
        echo ""
        echo "Resource Usage:"
        echo "- CPU Load: $(uptime | awk -F'load average:' '{print $2}')"
        echo "- Memory: $(free -h | grep Mem)"
        echo "- Disk: $(df -h /)"
        echo ""
        echo "Service Status:"
        systemctl status nginx php8.2-fpm mysql --no-pager -l
        echo ""
        echo "Recent Errors (last 24 hours):"
        journalctl --since "24 hours ago" --priority=err --no-pager
    } > "$report_file"
    
    print_status "System report generated: $report_file"
}

# Main monitoring function
main() {
    print_header "AbrajiAPIs System Monitor - $(date)"
    
    local exit_code=0
    
    # Run all checks
    check_disk_space || exit_code=1
    check_memory || exit_code=1
    check_cpu_load || exit_code=1
    check_services || exit_code=1
    check_website || exit_code=1
    check_database || exit_code=1
    check_log_sizes
    check_ssl_certificate
    
    # Maintenance tasks
    cleanup_old_files
    update_system
    
    # Generate report if there were issues
    if [ $exit_code -ne 0 ]; then
        generate_report
    fi
    
    print_header "Monitoring completed"
    
    if [ $exit_code -eq 0 ]; then
        print_status "All systems operational"
    else
        print_error "Issues detected - check logs and alerts"
    fi
    
    return $exit_code
}

# Check if script is run with specific action
case "${1:-monitor}" in
    "monitor")
        main
        ;;
    "report")
        generate_report
        ;;
    "cleanup")
        cleanup_old_files
        ;;
    "check-services")
        check_services
        ;;
    "check-website")
        check_website
        ;;
    *)
        echo "Usage: $0 [monitor|report|cleanup|check-services|check-website]"
        echo "  monitor       - Run full system monitoring (default)"
        echo "  report        - Generate system report"
        echo "  cleanup       - Clean up old files"
        echo "  check-services - Check service status"
        echo "  check-website  - Check website availability"
        exit 1
        ;;
esac
