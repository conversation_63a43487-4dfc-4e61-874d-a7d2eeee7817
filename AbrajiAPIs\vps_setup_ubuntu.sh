#!/bin/bash

# AbrajiAPIs VPS Setup Script for Ubuntu 20.04/22.04
# Run as root: sudo bash vps_setup_ubuntu.sh

set -e

echo "=========================================="
echo "  AbrajiAPIs VPS Setup - Ubuntu"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Get user input
read -p "Enter domain name (e.g., example.com): " DOMAIN_NAME
read -p "Enter database password for abrajiapis user: " DB_PASSWORD
read -p "Enter your email for SSL certificate: " EMAIL

print_status "Starting VPS setup for domain: $DOMAIN_NAME"

# Step 1: Update system
print_status "Updating system packages..."
apt update && apt upgrade -y
apt install -y curl wget git unzip software-properties-common ufw dnsutils bc

# Step 2: Install PHP 8.2
print_status "Installing PHP 8.2 and extensions..."
add-apt-repository ppa:ondrej/php -y
apt update
apt install -y php8.2 php8.2-fpm php8.2-cli php8.2-common \
    php8.2-mysql php8.2-pdo php8.2-zip php8.2-gd \
    php8.2-mbstring php8.2-curl php8.2-xml php8.2-bcmath \
    php8.2-intl php8.2-soap php8.2-sqlite3 php8.2-tokenizer \
    php8.2-xmlwriter php8.2-simplexml php8.2-dom

# Configure PHP
print_status "Configuring PHP..."
sed -i 's/memory_limit = .*/memory_limit = 512M/' /etc/php/8.2/fpm/php.ini
sed -i 's/max_execution_time = .*/max_execution_time = 300/' /etc/php/8.2/fpm/php.ini
sed -i 's/max_input_time = .*/max_input_time = 300/' /etc/php/8.2/fpm/php.ini
sed -i 's/post_max_size = .*/post_max_size = 100M/' /etc/php/8.2/fpm/php.ini
sed -i 's/upload_max_filesize = .*/upload_max_filesize = 100M/' /etc/php/8.2/fpm/php.ini

# Step 3: Install MySQL
print_status "Installing MySQL..."
apt install -y mysql-server
systemctl start mysql
systemctl enable mysql

# Secure MySQL installation
print_status "Securing MySQL installation..."

# Stop MySQL first
systemctl stop mysql

# Start MySQL in safe mode to reset password
print_status "Starting MySQL in safe mode..."
mysqld_safe --skip-grant-tables --skip-networking &
SAFE_PID=$!
sleep 10

# Reset root password
print_status "Setting root password..."
mysql -u root << MYSQL_EOF
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';
FLUSH PRIVILEGES;
EXIT;
MYSQL_EOF

# Stop safe mode
print_status "Stopping safe mode..."
kill $SAFE_PID 2>/dev/null || true
pkill mysqld_safe 2>/dev/null || true
pkill mysqld 2>/dev/null || true
sleep 5

# Start MySQL normally
systemctl start mysql
systemctl enable mysql
sleep 5

# Secure installation
print_status "Securing MySQL..."
mysql -u root -p$DB_PASSWORD << MYSQL_EOF
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';
FLUSH PRIVILEGES;
EXIT;
MYSQL_EOF

# Create database and user
print_status "Creating database and user..."
mysql -u root -p$DB_PASSWORD << MYSQL_EOF
CREATE DATABASE abrajiapis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'abrajiapis'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON abrajiapis.* TO 'abrajiapis'@'localhost';
FLUSH PRIVILEGES;
EXIT;
MYSQL_EOF

# Test connections
print_status "Testing database connections..."
if mysql -u root -p$DB_PASSWORD -e "SELECT 1;" &>/dev/null; then
    print_status "✅ Root connection successful"
else
    print_error "❌ Root connection failed"
    exit 1
fi

if mysql -u abrajiapis -p$DB_PASSWORD -e "USE abrajiapis; SELECT 1;" &>/dev/null; then
    print_status "✅ Abrajiapis user connection successful"
else
    print_error "❌ Abrajiapis user connection failed"
    exit 1
fi

# Step 4: Install Nginx
print_status "Installing Nginx..."
apt install -y nginx
systemctl start nginx
systemctl enable nginx

# Step 5: Install Composer
print_status "Installing Composer..."
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# Step 6: Create user and setup project directory
print_status "Creating abrajiapis user..."
sudo adduser --disabled-password --gecos "" abrajiapis
sudo usermod -aG www-data abrajiapis

# Create project directory
sudo mkdir -p /home/<USER>/AbrajiAPIs
sudo chown abrajiapis:www-data /home/<USER>/AbrajiAPIs

# Step 7: Configure Nginx
print_status "Configuring Nginx..."
sudo tee /etc/nginx/sites-available/abrajiapis << 'EOF'
server {
    listen 80;
    server_name abraji www.abraji;
    root /home/<USER>/AbrajiAPIs/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~ /\.(env|git) {
        deny all;
        return 404;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/abrajiapis /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx

# Step 8: Configure firewall
print_status "Configuring firewall..."
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw --force enable

# Step 9: Install SSL certificate
print_status "Installing SSL certificate..."
apt install -y certbot python3-certbot-nginx

# Check if domain resolves to this server
print_status "Checking domain resolution..."
SERVER_IP=$(curl -s ifconfig.me)
DOMAIN_IP=$(dig +short $DOMAIN_NAME)

if [ "$SERVER_IP" = "$DOMAIN_IP" ]; then
    print_status "Domain resolves correctly, installing SSL certificate..."
    certbot --nginx -d $DOMAIN_NAME -d www.$DOMAIN_NAME --email $EMAIL --agree-tos --non-interactive

    # Setup auto-renewal
    echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
    print_status "✅ SSL certificate installed successfully"
else
    print_warning "Domain does not resolve to this server yet"
    print_warning "Server IP: $SERVER_IP"
    print_warning "Domain IP: $DOMAIN_IP"
    print_warning "Please update your DNS records and run: certbot --nginx -d $DOMAIN_NAME -d www.$DOMAIN_NAME"
fi

# Step 10: Configure PHP-FPM and services
print_status "Starting services..."
systemctl start php8.2-fpm
systemctl enable php8.2-fpm
systemctl restart nginx

# Step 11: Create deployment script
print_status "Creating deployment script..."
cat > /home/<USER>/deploy.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/AbrajiAPIs

# Pull latest changes (if using git)
# git pull origin main

# Install/update dependencies
composer install --no-dev --optimize-autoloader

# Run migrations
php artisan migrate --force

# Clear and cache config
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
sudo chown -R abrajiapis:www-data /home/<USER>/AbrajiAPIs
sudo find /home/<USER>/AbrajiAPIs -type f -exec chmod 644 {} \;
sudo find /home/<USER>/AbrajiAPIs -type d -exec chmod 755 {} \;
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/storage
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/bootstrap/cache

echo "Deployment completed!"
EOF

chmod +x /home/<USER>/deploy.sh
chown abrajiapis:abrajiapis /home/<USER>/deploy.sh

# Step 12: Create backup script
print_status "Creating backup script..."
mkdir -p /home/<USER>/backups
cat > /home/<USER>/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
PROJECT_DIR="/home/<USER>/AbrajiAPIs"

mkdir -p \$BACKUP_DIR

# Database backup
mysqldump -u abrajiapis -p$DB_PASSWORD abrajiapis > \$BACKUP_DIR/database_\$DATE.sql

# Files backup
tar -czf \$BACKUP_DIR/files_\$DATE.tar.gz \$PROJECT_DIR

# Remove old backups (older than 7 days)
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: \$DATE"
EOF

chmod +x /home/<USER>/backup.sh
chown abrajiapis:abrajiapis /home/<USER>/backup.sh

# Setup daily backup
echo "0 2 * * * /home/<USER>/backup.sh" | sudo -u abrajiapis crontab -

# Step 13: Create .env template
print_status "Creating .env template..."
cat > /home/<USER>/.env.template << EOF
APP_NAME=AbrajiAPIs
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://$DOMAIN_NAME

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=abrajiapis
DB_USERNAME=abrajiapis
DB_PASSWORD=$DB_PASSWORD

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# SAS Radius Configuration
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost
EOF

chown abrajiapis:abrajiapis /home/<USER>/.env.template

print_status "=========================================="
print_status "VPS Setup completed successfully!"
print_status "=========================================="
print_status ""
print_status "Next steps:"
print_status "1. Upload your AbrajiAPIs project files to: /home/<USER>/AbrajiAPIs/"
print_status "2. Copy .env.template to .env and configure it"
print_status "3. Run the deployment script: /home/<USER>/deploy.sh"
print_status "4. Visit your site: https://$DOMAIN_NAME"
print_status ""
print_status "Important files:"
print_status "- Project directory: /home/<USER>/AbrajiAPIs/"
print_status "- Nginx config: /etc/nginx/sites-available/abrajiapis"
print_status "- Deploy script: /home/<USER>/deploy.sh"
print_status "- Backup script: /home/<USER>/backup.sh"
print_status "- Backups location: /home/<USER>/backups/"
print_status ""
print_status "Database details:"
print_status "- Database: abrajiapis"
print_status "- Username: abrajiapis"
print_status "- Password: $DB_PASSWORD"
print_status ""
print_warning "Please save the database password securely!"
print_status "=========================================="
echo "🎉 VPS setup completed!"



