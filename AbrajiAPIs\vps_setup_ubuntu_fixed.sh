#!/bin/bash

# AbrajiAPIs VPS Setup Script for Ubuntu 20.04/22.04 - Fixed Version
# Run as root: sudo bash vps_setup_ubuntu_fixed.sh

set -e

echo "=========================================="
echo "  AbrajiAPIs VPS Setup - Ubuntu (Fixed)"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Get user input
read -p "Enter domain name (e.g., example.com): " DOMAIN_NAME
read -s -p "Enter database password for abrajiapis user: " DB_PASSWORD
echo ""
read -p "Enter your email for SSL certificate: " EMAIL

print_status "Starting VPS setup for domain: $DOMAIN_NAME"

# Step 1: Update system
print_header "Step 1: Updating System"
print_status "Updating system packages..."
apt update && apt upgrade -y
apt install -y curl wget git unzip software-properties-common ufw dnsutils bc htop nano

# Clean up old kernels
print_status "Cleaning up old packages..."
apt autoremove -y

# Step 2: Install PHP 8.2
print_header "Step 2: Installing PHP 8.2"
print_status "Adding PHP repository..."
add-apt-repository ppa:ondrej/php -y
apt update

print_status "Installing PHP 8.2 and extensions..."
apt install -y php8.2 php8.2-fpm php8.2-cli php8.2-common \
    php8.2-mysql php8.2-zip php8.2-gd php8.2-mbstring \
    php8.2-curl php8.2-xml php8.2-bcmath php8.2-intl \
    php8.2-soap php8.2-sqlite3

# Configure PHP
print_status "Configuring PHP..."
sed -i 's/memory_limit = .*/memory_limit = 512M/' /etc/php/8.2/fpm/php.ini
sed -i 's/max_execution_time = .*/max_execution_time = 300/' /etc/php/8.2/fpm/php.ini
sed -i 's/max_input_time = .*/max_input_time = 300/' /etc/php/8.2/fpm/php.ini
sed -i 's/post_max_size = .*/post_max_size = 100M/' /etc/php/8.2/fpm/php.ini
sed -i 's/upload_max_filesize = .*/upload_max_filesize = 100M/' /etc/php/8.2/fpm/php.ini
sed -i 's/;date.timezone =.*/date.timezone = UTC/' /etc/php/8.2/fpm/php.ini

# Step 3: Install and configure MySQL
print_header "Step 3: Installing MySQL"
print_status "Installing MySQL server..."
apt install -y mysql-server

print_status "Configuring MySQL..."
# Stop MySQL to configure it properly
systemctl stop mysql

# Start MySQL in safe mode
print_status "Starting MySQL in safe mode..."
mysqld_safe --skip-grant-tables --skip-networking &
SAFE_PID=$!
sleep 15

# Configure root user
print_status "Setting up MySQL root user..."
mysql -u root << MYSQL_COMMANDS
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';
FLUSH PRIVILEGES;
EXIT;
MYSQL_COMMANDS

# Stop safe mode
print_status "Stopping safe mode..."
kill $SAFE_PID 2>/dev/null || true
pkill mysqld_safe 2>/dev/null || true
pkill mysqld 2>/dev/null || true
sleep 5

# Start MySQL normally
systemctl start mysql
systemctl enable mysql
sleep 5

# Secure MySQL and create database
print_status "Securing MySQL and creating database..."
mysql -u root -p$DB_PASSWORD << MYSQL_COMMANDS
-- Remove anonymous users
DELETE FROM mysql.user WHERE User='';
-- Remove remote root
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
-- Remove test database
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';
-- Create application database
CREATE DATABASE abrajiapis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- Create application user
CREATE USER 'abrajiapis'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON abrajiapis.* TO 'abrajiapis'@'localhost';
FLUSH PRIVILEGES;
EXIT;
MYSQL_COMMANDS

# Test database connections
print_status "Testing database connections..."
if mysql -u root -p$DB_PASSWORD -e "SELECT 1;" &>/dev/null; then
    print_status "✅ MySQL root connection successful"
else
    print_error "❌ MySQL root connection failed"
    exit 1
fi

if mysql -u abrajiapis -p$DB_PASSWORD -e "USE abrajiapis; SELECT 1;" &>/dev/null; then
    print_status "✅ Application database connection successful"
else
    print_error "❌ Application database connection failed"
    exit 1
fi

# Step 4: Install Nginx
print_header "Step 4: Installing Nginx"
print_status "Installing Nginx..."
apt install -y nginx
systemctl start nginx
systemctl enable nginx

# Step 5: Install Composer
print_header "Step 5: Installing Composer"
print_status "Installing Composer..."
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer
chmod +x /usr/local/bin/composer

# Verify Composer installation
if composer --version &>/dev/null; then
    print_status "✅ Composer installed successfully"
else
    print_error "❌ Composer installation failed"
    exit 1
fi

# Step 6: Create application user and directories
print_header "Step 6: Setting up Application User"
print_status "Creating abrajiapis user..."
adduser --disabled-password --gecos "" abrajiapis 2>/dev/null || print_warning "User abrajiapis already exists"
usermod -aG www-data abrajiapis

# Create directories
print_status "Creating application directories..."
mkdir -p /home/<USER>/AbrajiAPIs
mkdir -p /home/<USER>/backups
chown abrajiapis:www-data /home/<USER>/AbrajiAPIs
chown abrajiapis:abrajiapis /home/<USER>/backups

# Step 7: Configure Nginx
print_header "Step 7: Configuring Nginx"
print_status "Creating Nginx configuration..."
cat > /etc/nginx/sites-available/abrajiapis << EOF
server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;
    root /home/<USER>/AbrajiAPIs/public;
    index index.php index.html index.htm;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Main location
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
    }

    # Deny access to hidden files
    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security: Hide sensitive files
    location ~ /\.(env|git|htaccess) {
        deny all;
        return 404;
    }

    # Deny access to vendor and other sensitive directories
    location ~ ^/(vendor|storage|bootstrap/cache) {
        deny all;
        return 404;
    }
}
EOF

# Enable site and test configuration
print_status "Enabling Nginx site..."
ln -sf /etc/nginx/sites-available/abrajiapis /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

if nginx -t; then
    print_status "✅ Nginx configuration is valid"
    systemctl reload nginx
else
    print_error "❌ Nginx configuration has errors"
    exit 1
fi

# Step 8: Configure firewall
print_header "Step 8: Configuring Firewall"
print_status "Setting up UFW firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw --force enable

print_status "✅ Firewall configured successfully"

# Step 9: Start services
print_header "Step 9: Starting Services"
print_status "Starting PHP-FPM..."
systemctl start php8.2-fpm
systemctl enable php8.2-fpm

print_status "Restarting Nginx..."
systemctl restart nginx

# Verify services
if systemctl is-active --quiet php8.2-fpm; then
    print_status "✅ PHP-FPM is running"
else
    print_error "❌ PHP-FPM failed to start"
fi

if systemctl is-active --quiet nginx; then
    print_status "✅ Nginx is running"
else
    print_error "❌ Nginx failed to start"
fi

if systemctl is-active --quiet mysql; then
    print_status "✅ MySQL is running"
else
    print_error "❌ MySQL is not running"
fi

# Step 10: Create deployment scripts
print_header "Step 10: Creating Deployment Scripts"
print_status "Creating deployment script..."
cat > /home/<USER>/deploy.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/AbrajiAPIs

echo "🚀 Starting deployment..."

# Check if Laravel project exists
if [ ! -f "artisan" ]; then
    echo "❌ Laravel project not found in current directory"
    exit 1
fi

# Install/update dependencies
if [ -f "composer.json" ]; then
    echo "📦 Installing dependencies..."
    composer install --no-dev --optimize-autoloader --no-interaction
    echo "✅ Dependencies installed"
fi

# Generate app key if needed
if [ -f ".env" ] && ! grep -q "APP_KEY=base64:" .env; then
    echo "🔑 Generating application key..."
    php artisan key:generate --force
    echo "✅ App key generated"
fi

# Run migrations
echo "🗄️ Running database migrations..."
php artisan migrate --force
echo "✅ Migrations completed"

# Clear and cache config
echo "🧹 Clearing and caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link 2>/dev/null || echo "Storage link already exists"

# Set proper permissions
echo "🔒 Setting permissions..."
sudo chown -R abrajiapis:www-data /home/<USER>/AbrajiAPIs
sudo find /home/<USER>/AbrajiAPIs -type f -exec chmod 644 {} \;
sudo find /home/<USER>/AbrajiAPIs -type d -exec chmod 755 {} \;
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/storage
sudo chmod -R 775 /home/<USER>/AbrajiAPIs/bootstrap/cache

echo "🎉 Deployment completed successfully!"
EOF

chmod +x /home/<USER>/deploy.sh
chown abrajiapis:abrajiapis /home/<USER>/deploy.sh

print_status "Creating backup script..."
cat > /home/<USER>/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
PROJECT_DIR="/home/<USER>/AbrajiAPIs"

echo "📦 Starting backup process..."

mkdir -p \$BACKUP_DIR

# Database backup
echo "🗄️ Backing up database..."
if mysqldump -u abrajiapis -p$DB_PASSWORD abrajiapis > \$BACKUP_DIR/database_\$DATE.sql 2>/dev/null; then
    echo "✅ Database backup created: database_\$DATE.sql"
else
    echo "❌ Database backup failed"
fi

# Files backup
if [ -d "\$PROJECT_DIR" ]; then
    echo "📁 Backing up project files..."
    if tar -czf \$BACKUP_DIR/files_\$DATE.tar.gz \$PROJECT_DIR 2>/dev/null; then
        echo "✅ Files backup created: files_\$DATE.tar.gz"
    else
        echo "❌ Files backup failed"
    fi
fi

# Remove old backups (older than 7 days)
echo "🧹 Cleaning old backups..."
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete 2>/dev/null
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete 2>/dev/null

echo "🎉 Backup completed: \$DATE"
EOF

chmod +x /home/<USER>/backup.sh
chown abrajiapis:abrajiapis /home/<USER>/backup.sh

# Setup daily backup cron job
print_status "Setting up daily backup cron job..."
echo "0 2 * * * /home/<USER>/backup.sh >> /home/<USER>/backup.log 2>&1" | sudo -u abrajiapis crontab -

# Step 11: Create environment template
print_header "Step 11: Creating Environment Template"
print_status "Creating .env template..."
cat > /home/<USER>/.env.template << EOF
APP_NAME=AbrajiAPIs
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://$DOMAIN_NAME

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=abrajiapis
DB_USERNAME=abrajiapis
DB_PASSWORD=$DB_PASSWORD

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# SAS Radius Configuration
API_DOMAIN=http://localhost
SAS_RADIUS_DEFAULT_URL=http://localhost

# Mail Configuration (optional)
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="hello@$DOMAIN_NAME"
MAIL_FROM_NAME="\${APP_NAME}"
EOF

chown abrajiapis:abrajiapis /home/<USER>/.env.template

# Step 12: Install SSL certificate
print_header "Step 12: Installing SSL Certificate"
print_status "Installing Certbot..."
apt install -y certbot python3-certbot-nginx

# Check if domain resolves to this server
print_status "Checking domain resolution..."
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s icanhazip.com 2>/dev/null || echo "unknown")
DOMAIN_IP=$(dig +short $DOMAIN_NAME 2>/dev/null | head -n1)

print_status "Server IP: $SERVER_IP"
print_status "Domain IP: $DOMAIN_IP"

if [ "$SERVER_IP" = "$DOMAIN_IP" ] && [ "$DOMAIN_IP" != "" ]; then
    print_status "Domain resolves correctly, installing SSL certificate..."
    if certbot --nginx -d $DOMAIN_NAME -d www.$DOMAIN_NAME --email $EMAIL --agree-tos --non-interactive; then
        print_status "✅ SSL certificate installed successfully"

        # Setup auto-renewal
        echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
        print_status "✅ SSL auto-renewal configured"
    else
        print_warning "SSL certificate installation failed, but you can try again later"
    fi
else
    print_warning "Domain does not resolve to this server yet"
    print_warning "Please update your DNS records to point $DOMAIN_NAME to $SERVER_IP"
    print_warning "Then run: certbot --nginx -d $DOMAIN_NAME -d www.$DOMAIN_NAME"
fi

# Step 13: Create monitoring script
print_header "Step 13: Creating Monitoring Script"
print_status "Creating system monitor script..."
cat > /home/<USER>/monitor.sh << 'EOF'
#!/bin/bash
echo "🔍 AbrajiAPIs System Status - $(date)"
echo "=================================="

# Check services
echo "📊 Service Status:"
systemctl is-active --quiet nginx && echo "✅ Nginx: Running" || echo "❌ Nginx: Stopped"
systemctl is-active --quiet php8.2-fpm && echo "✅ PHP-FPM: Running" || echo "❌ PHP-FPM: Stopped"
systemctl is-active --quiet mysql && echo "✅ MySQL: Running" || echo "❌ MySQL: Stopped"

# Check disk space
echo ""
echo "💾 Disk Usage:"
df -h / | tail -1 | awk '{print "Used: " $3 " / " $2 " (" $5 ")"}'

# Check memory
echo ""
echo "🧠 Memory Usage:"
free -h | grep Mem | awk '{print "Used: " $3 " / " $2}'

# Check load
echo ""
echo "⚡ System Load:"
uptime | awk -F'load average:' '{print "Load:" $2}'

# Check recent errors
echo ""
echo "🚨 Recent Errors (last 10):"
tail -10 /var/log/nginx/error.log 2>/dev/null | grep -v "No such file" || echo "No recent Nginx errors"

echo ""
echo "=================================="
EOF

chmod +x /home/<USER>/monitor.sh
chown abrajiapis:abrajiapis /home/<USER>/monitor.sh

# Step 14: Final system optimization
print_header "Step 14: Final System Optimization"
print_status "Optimizing system settings..."

# Optimize MySQL
cat > /etc/mysql/mysql.conf.d/abrajiapis.cnf << 'EOF'
[mysqld]
# Basic settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Query cache
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 2M

# Connection settings
max_connections = 100
connect_timeout = 10
wait_timeout = 600
max_allowed_packet = 64M

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
EOF

# Restart MySQL to apply settings
systemctl restart mysql

# Enable OPcache for PHP
cat > /etc/php/8.2/fpm/conf.d/10-opcache.ini << 'EOF'
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
EOF

systemctl restart php8.2-fpm

print_status "✅ System optimization completed"

# Final cleanup
print_status "Performing final cleanup..."
apt autoremove -y
apt autoclean

print_header "🎉 VPS Setup Completed Successfully!"
print_status ""
print_status "📋 Setup Summary:"
print_status "=================="
print_status "✅ PHP 8.2 with all required extensions"
print_status "✅ MySQL 8.0 with abrajiapis database"
print_status "✅ Nginx web server configured"
print_status "✅ SSL certificate ready (if domain resolves)"
print_status "✅ Firewall configured (UFW)"
print_status "✅ Daily backups scheduled"
print_status "✅ Deployment scripts created"
print_status "✅ System monitoring tools"
print_status ""
print_status "📁 Important Files:"
print_status "==================="
print_status "• Project directory: /home/<USER>/AbrajiAPIs/"
print_status "• Environment template: /home/<USER>/.env.template"
print_status "• Deploy script: /home/<USER>/deploy.sh"
print_status "• Backup script: /home/<USER>/backup.sh"
print_status "• Monitor script: /home/<USER>/monitor.sh"
print_status "• Nginx config: /etc/nginx/sites-available/abrajiapis"
print_status ""
print_status "🔐 Database Credentials:"
print_status "========================"
print_status "• Database: abrajiapis"
print_status "• Username: abrajiapis"
print_status "• Password: $DB_PASSWORD"
print_status ""
print_status "🚀 Next Steps:"
print_status "=============="
print_status "1. Upload your AbrajiAPIs project files to: /home/<USER>/AbrajiAPIs/"
print_status "2. Copy .env.template to .env and configure: cp /home/<USER>/.env.template /home/<USER>/AbrajiAPIs/.env"
print_status "3. Run deployment: bash /home/<USER>/deploy.sh"
print_status "4. Test your site: https://$DOMAIN_NAME"
print_status ""
print_status "🔧 Useful Commands:"
print_status "==================="
print_status "• Check system status: bash /home/<USER>/monitor.sh"
print_status "• Manual backup: bash /home/<USER>/backup.sh"
print_status "• View logs: tail -f /var/log/nginx/error.log"
print_status "• Restart services: systemctl restart nginx php8.2-fpm mysql"
print_status ""
print_warning "⚠️  Important: Save the database password securely!"
print_status ""
print_header "🎊 Your VPS is ready for AbrajiAPIs deployment!"
